<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للمخططات</title>
    <style>
        body { 
            font-family: Tahoma; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas { 
            border: 2px solid #3498db; 
            border-radius: 8px;
            display: block;
            margin: 20px auto;
            background: white;
        }
        .toolbar {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: Tahoma;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn.active { 
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
            transform: scale(1.05);
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 اختبار تطبيق المخططات الذكية</h1>
        
        <div id="status" class="status">جاري التحميل...</div>
        
        <div class="toolbar">
            <button id="rectBtn" class="btn btn-primary active" data-tool="rectangle">🟦 مربع</button>
            <button id="circleBtn" class="btn btn-success" data-tool="circle">🔴 دائرة</button>
            <button id="diamondBtn" class="btn btn-warning" data-tool="diamond">🟨 معين</button>
            <button id="connectBtn" class="btn btn-danger" data-tool="connect">🔗 ربط</button>
        </div>
        
        <canvas id="testCanvas" width="800" height="600"></canvas>
        
        <div style="text-align: center;">
            <p><strong>التعليمات:</strong></p>
            <p>1. اختر نوع العقدة من الأزرار أعلاه</p>
            <p>2. انقر في أي مكان على الكانفاس لإنشاء عقدة</p>
            <p>3. اختر أداة الربط وانقر على عقدتين لربطهما</p>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let canvas, ctx;
        let currentTool = 'rectangle';
        let nodes = [];
        let connections = [];
        let selectedNode = null;
        let isConnecting = false;
        let connectionStart = null;

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            try {
                canvas = document.getElementById('testCanvas');
                ctx = canvas.getContext('2d');
                
                if (!ctx) {
                    throw new Error('فشل في تهيئة Canvas');
                }
                
                setupEventListeners();
                render();
                
                document.getElementById('status').innerHTML = '<span class="success">✅ التطبيق جاهز للاستخدام!</span>';
                
            } catch (error) {
                console.error('خطأ:', error);
                document.getElementById('status').innerHTML = '<span class="error">❌ خطأ: ' + error.message + '</span>';
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // أزرار الأدوات
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentTool = this.getAttribute('data-tool');
                    
                    if (currentTool !== 'connect') {
                        isConnecting = false;
                        connectionStart = null;
                    }
                });
            });

            // النقر على الكانفاس
            canvas.addEventListener('click', function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                if (currentTool === 'connect') {
                    handleConnection(x, y);
                } else {
                    createNode(x, y, currentTool);
                }
            });
        }

        // إنشاء عقدة جديدة
        function createNode(x, y, shape) {
            const node = {
                id: Date.now(),
                x: x - 75,
                y: y - 50,
                width: 150,
                height: 100,
                shape: shape,
                text: 'عقدة جديدة',
                color: getShapeColor(shape)
            };
            
            nodes.push(node);
            render();
        }

        // الحصول على لون الشكل
        function getShapeColor(shape) {
            const colors = {
                'rectangle': '#3498db',
                'circle': '#27ae60',
                'diamond': '#f39c12'
            };
            return colors[shape] || '#3498db';
        }

        // التعامل مع الربط
        function handleConnection(x, y) {
            const clickedNode = getNodeAt(x, y);
            
            if (!clickedNode) return;
            
            if (!connectionStart) {
                connectionStart = clickedNode;
                isConnecting = true;
            } else if (connectionStart !== clickedNode) {
                createConnection(connectionStart, clickedNode);
                connectionStart = null;
                isConnecting = false;
            }
        }

        // إنشاء رابط
        function createConnection(fromNode, toNode) {
            const connection = {
                id: Date.now(),
                from: fromNode.id,
                to: toNode.id,
                fromNode: fromNode,
                toNode: toNode
            };
            
            connections.push(connection);
            render();
        }

        // البحث عن عقدة في موقع معين
        function getNodeAt(x, y) {
            for (let node of nodes) {
                if (x >= node.x && x <= node.x + node.width &&
                    y >= node.y && y <= node.y + node.height) {
                    return node;
                }
            }
            return null;
        }

        // رسم كل شيء
        function render() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // رسم الروابط
            connections.forEach(connection => {
                drawConnection(connection);
            });
            
            // رسم العقد
            nodes.forEach(node => {
                drawNode(node);
            });
        }

        // رسم عقدة
        function drawNode(node) {
            ctx.save();
            ctx.fillStyle = node.color;
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            
            switch (node.shape) {
                case 'rectangle':
                    ctx.fillRect(node.x, node.y, node.width, node.height);
                    ctx.strokeRect(node.x, node.y, node.width, node.height);
                    break;
                case 'circle':
                    const centerX = node.x + node.width / 2;
                    const centerY = node.y + node.height / 2;
                    const radius = Math.min(node.width, node.height) / 2;
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();
                    break;
                case 'diamond':
                    const cx = node.x + node.width / 2;
                    const cy = node.y + node.height / 2;
                    ctx.beginPath();
                    ctx.moveTo(cx, node.y);
                    ctx.lineTo(node.x + node.width, cy);
                    ctx.lineTo(cx, node.y + node.height);
                    ctx.lineTo(node.x, cy);
                    ctx.closePath();
                    ctx.fill();
                    ctx.stroke();
                    break;
            }
            
            // رسم النص
            ctx.fillStyle = 'white';
            ctx.font = '14px Tahoma';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(node.text, node.x + node.width / 2, node.y + node.height / 2);
            
            ctx.restore();
        }

        // رسم رابط
        function drawConnection(connection) {
            const fromCenter = {
                x: connection.fromNode.x + connection.fromNode.width / 2,
                y: connection.fromNode.y + connection.fromNode.height / 2
            };
            const toCenter = {
                x: connection.toNode.x + connection.toNode.width / 2,
                y: connection.toNode.y + connection.toNode.height / 2
            };
            
            ctx.save();
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            
            // رسم الخط
            ctx.beginPath();
            ctx.moveTo(fromCenter.x, fromCenter.y);
            ctx.lineTo(toCenter.x, toCenter.y);
            ctx.stroke();
            
            // رسم رأس السهم
            const angle = Math.atan2(toCenter.y - fromCenter.y, toCenter.x - fromCenter.x);
            const arrowSize = 15;
            
            ctx.beginPath();
            ctx.moveTo(toCenter.x, toCenter.y);
            ctx.lineTo(
                toCenter.x - arrowSize * Math.cos(angle - Math.PI / 6),
                toCenter.y - arrowSize * Math.sin(angle - Math.PI / 6)
            );
            ctx.moveTo(toCenter.x, toCenter.y);
            ctx.lineTo(
                toCenter.x - arrowSize * Math.cos(angle + Math.PI / 6),
                toCenter.y - arrowSize * Math.sin(angle + Math.PI / 6)
            );
            ctx.stroke();
            
            ctx.restore();
        }
    </script>
</body>
</html>
