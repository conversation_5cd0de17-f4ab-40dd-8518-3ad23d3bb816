# تطبيق المخططات التقنية

تطبيق ويب متطور لإنشاء المخططات التقنية مع دعم الصور والنصوص العربية، مصمم خصيصاً للاستخدام الشخصي.

## المميزات

### 🎨 أدوات الرسم المتقدمة
- **المربعات والمستطيلات** مع إمكانية التحكم في الألوان والحدود
- **الدوائر والأشكال البيضاوية** قابلة للتخصيص
- **الأسهم** لربط العناصر ببعضها البعض
- **النصوص** مع دعم كامل للغة العربية
- **الصور** مع إمكانية الرفع والتحكم في الحجم

### 🛠️ أدوات التحكم
- **أداة التحديد** لتحريك وتعديل العناصر
- **لوحة الخصائص** لتخصيص كل عنصر
- **التحكم في الألوان** (لون الحدود ولون التعبئة)
- **التحكم في سمك الخطوط** وحجم النصوص
- **اختيار نوع الخط** المناسب للنصوص العربية

### 💾 إدارة المخططات
- **حفظ المخططات** مع إمكانية إضافة اسم ووصف
- **تحميل المخططات المحفوظة** بسهولة
- **تصدير كصورة PNG** عالية الجودة
- **حفظ محلي** في المتصفح أو قاعدة بيانات

### 🌐 دعم اللغة العربية
- **واجهة عربية كاملة** من اليمين إلى اليسار
- **دعم النصوص العربية** في المخططات
- **خطوط عربية** مناسبة للمخططات التقنية

## متطلبات التشغيل

### الحد الأدنى
- **خادم ويب** (Apache/Nginx)
- **PHP 7.4+** 
- **متصفح حديث** يدعم HTML5 Canvas

### للميزات المتقدمة (اختياري)
- **MySQL/MariaDB** لحفظ المخططات في قاعدة البيانات
- **مساحة تخزين** لرفع الصور

## التثبيت والإعداد

### 1. نسخ الملفات
```bash
# نسخ المشروع إلى مجلد الخادم
cp -r diagram-app /path/to/your/webserver/
```

### 2. إعداد قاعدة البيانات (اختياري)
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE diagram_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحديث إعدادات الاتصال في config/database.php
```

### 3. ضبط الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 uploads/
chmod 755 uploads/images/
```

### 4. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost/diagram-app/`

## كيفية الاستخدام

### إنشاء مخطط جديد

1. **اختر الأداة المناسبة** من شريط الأدوات
2. **ارسم على الكانفاس** بالنقر والسحب
3. **أضف النصوص** بالنقر المزدوج أو استخدام أداة النص
4. **ارفع الصور** من خلال زر رفع الصور
5. **خصص الألوان والخصائص** من لوحة التحكم

### تحرير العناصر

1. **حدد العنصر** باستخدام أداة التحديد
2. **اسحب لتحريك** العنصر
3. **غير الخصائص** من لوحة الخصائص
4. **احذف العنصر** بالضغط على Delete أو زر الحذف

### حفظ وتحميل المخططات

1. **احفظ المخطط** بالضغط على زر "حفظ المخطط"
2. **أدخل اسم ووصف** للمخطط
3. **حمل المخططات المحفوظة** من زر "تحميل مخطط"
4. **صدر كصورة** بالضغط على زر "تصدير كصورة"

## الاختصارات

- **Delete**: حذف العنصر المحدد
- **Ctrl+Z**: التراجع (قريباً)
- **Double Click**: إضافة نص في الموقع المحدد

## هيكل المشروع

```
diagram-app/
├── index.php              # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── style.css      # ملف التنسيقات
│   └── js/
│       ├── diagram-engine.js  # محرك الرسم
│       └── main.js        # الوظائف الرئيسية
├── api/                   # واجهات برمجة التطبيقات
│   ├── save_diagram.php   # حفظ المخططات
│   ├── load_diagrams.php  # تحميل قائمة المخططات
│   ├── get_diagram.php    # تحميل مخطط محدد
│   └── upload_image.php   # رفع الصور
├── config/
│   └── database.php       # إعدادات قاعدة البيانات
├── uploads/
│   └── images/           # مجلد الصور المرفوعة
└── README.md             # هذا الملف
```

## التطوير المستقبلي

- [ ] إضافة المزيد من الأشكال الهندسية
- [ ] وظيفة التراجع والإعادة (Undo/Redo)
- [ ] طبقات متعددة للرسم
- [ ] تصدير بصيغ مختلفة (SVG, PDF)
- [ ] مشاركة المخططات مع الآخرين
- [ ] قوالب جاهزة للمخططات الشائعة

## الدعم والمساعدة

هذا التطبيق مصمم للاستخدام الشخصي ويمكن تخصيصه حسب احتياجاتك.

### نصائح للاستخدام الأمثل

1. **استخدم ألوان متناسقة** لجعل المخطط أكثر وضوحاً
2. **اختر أحجام نصوص مناسبة** للقراءة
3. **نظم العناصر** بشكل منطقي ومتسلسل
4. **احفظ نسخ احتياطية** من مخططاتك المهمة

---

**تم تطوير هذا التطبيق خصيصاً لإنشاء مخططات تقنية احترافية مع دعم كامل للغة العربية** 🚀
