# تطبيق المخططات التقنية الذكية

تطبيق ويب متطور وذكي لإنشاء المخططات التقنية باستخدام نظام **العقد الذكية** مع دعم الصور والنصوص العربية، مصمم خصيصاً للاستخدام الشخصي والمهني.

## 🚀 المميزات الجديدة والذكية

### 🧠 نظام العقد الذكية (Smart Nodes)
- **عقد متكاملة** تحتوي على شكل + نص + صورة في عنصر واحد
- **ربط تلقائي ذكي** بين العقد بنقرتين فقط
- **نقاط اتصال تلقائية** تختار أفضل موقع للربط
- **تحرير مباشر** للنص والصور داخل العقدة

### 🎨 أشكال العقد المتنوعة
- **عقد مربعة** للعمليات والمهام العادية
- **عقد دائرية** للنقاط المهمة والبدايات
- **عقد معين** للقرارات والتفرعات
- **ربط ذكي** بأسهم تلقائية بين العقد

### 🛠️ أدوات التحكم
- **أداة التحديد** لتحريك وتعديل العناصر
- **لوحة الخصائص** لتخصيص كل عنصر
- **التحكم في الألوان** (لون الحدود ولون التعبئة)
- **التحكم في سمك الخطوط** وحجم النصوص
- **اختيار نوع الخط** المناسب للنصوص العربية

### 💾 إدارة المخططات
- **حفظ المخططات** مع إمكانية إضافة اسم ووصف
- **تحميل المخططات المحفوظة** بسهولة
- **تصدير كصورة PNG** عالية الجودة
- **حفظ محلي** في المتصفح أو قاعدة بيانات

### 🌐 دعم اللغة العربية
- **واجهة عربية كاملة** من اليمين إلى اليسار
- **دعم النصوص العربية** في المخططات
- **خطوط عربية** مناسبة للمخططات التقنية

## متطلبات التشغيل

### الحد الأدنى
- **خادم ويب** (Apache/Nginx)
- **PHP 7.4+** 
- **متصفح حديث** يدعم HTML5 Canvas

### للميزات المتقدمة (اختياري)
- **MySQL/MariaDB** لحفظ المخططات في قاعدة البيانات
- **مساحة تخزين** لرفع الصور

## التثبيت والإعداد

### 1. نسخ الملفات
```bash
# نسخ المشروع إلى مجلد الخادم
cp -r diagram-app /path/to/your/webserver/
```

### 2. إعداد قاعدة البيانات (اختياري)
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE diagram_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحديث إعدادات الاتصال في config/database.php
```

### 3. ضبط الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 uploads/
chmod 755 uploads/images/
```

### 4. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost/diagram-app/`

## 🎯 كيفية الاستخدام الذكي

### إنشاء مخطط بالطريقة الذكية

1. **اختر نوع العقدة** (مربع/دائرة/معين) من شريط الأدوات
2. **انقر في أي مكان** لإنشاء عقدة جديدة تلقائياً
3. **اكتب النص مباشرة** في حقل تحرير النص
4. **ارفع صورة** للعقدة من زر تحديث الصورة
5. **اختر أداة الربط** وانقر على عقدتين لربطهما تلقائياً

### تحرير العقد الذكية

1. **انقر على العقدة** لتحديدها
2. **اسحب العقدة** لتحريكها (الروابط تتحرك تلقائياً)
3. **اكتب النص الجديد** في الحقل وانقر "تحديث النص"
4. **ارفع صورة جديدة** وانقر "تحديث الصورة"
5. **احذف العقدة** بالضغط على Delete أو زر "حذف المحدد"

### ربط العقد بذكاء

1. **اختر أداة الربط** (أيقونة السلسلة)
2. **انقر على العقدة الأولى** (ستظهر بلون مميز)
3. **انقر على العقدة الثانية** (سيتم الربط تلقائياً)
4. **الرابط يختار أفضل نقاط الاتصال** تلقائياً

### حفظ وتحميل المخططات

1. **احفظ المخطط** بالضغط على زر "حفظ المخطط"
2. **أدخل اسم ووصف** للمخطط
3. **حمل المخططات المحفوظة** من زر "تحميل مخطط"
4. **صدر كصورة** بالضغط على زر "تصدير كصورة"

## 📋 مثال عملي: إنشاء مخطط نظام سحب الهواء

لإنشاء مخطط مثل المخطط الذي أرسلته:

### الخطوة 1: إنشاء العقدة الرئيسية
1. اختر **عقدة مربعة** من شريط الأدوات
2. انقر في أعلى الكانفاس لإنشاء العقدة
3. اكتب "نظام سحب الهواء في المحرك - Intake System"
4. انقر **تحديث النص**

### الخطوة 2: إنشاء العقد الفرعية
1. أنشئ عقدة مربعة وأكتب "Forced induction | دخول الهواء مضغوط"
2. أنشئ عقدة مربعة وأكتب "Natural Induction | دخول هواء طبيعي"
3. أنشئ عقدة مربعة وأكتب "فلتر الهواء | air filter"

### الخطوة 3: إضافة الصور
1. حدد كل عقدة واحدة تلو الأخرى
2. ارفع الصورة المناسبة لكل عقدة
3. انقر **تحديث الصورة**

### الخطوة 4: ربط العقد
1. اختر **أداة الربط** من شريط الأدوات
2. انقر على العقدة الرئيسية ثم على العقدة الفرعية
3. كرر العملية لجميع الروابط المطلوبة

### النتيجة
ستحصل على مخطط احترافي مع:
- عقد منظمة ومرتبة
- نصوص عربية وإنجليزية واضحة
- صور توضيحية لكل عنصر
- روابط ذكية تربط العناصر ببعضها

## ⌨️ الاختصارات

- **Delete**: حذف العقدة أو الرابط المحدد
- **Ctrl+Z**: التراجع (قريباً)
- **Enter**: تحديث النص في العقدة المحددة
- **Escape**: إلغاء عملية الربط الحالية

## هيكل المشروع

```
diagram-app/
├── index.php              # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── style.css      # ملف التنسيقات
│   └── js/
│       ├── diagram-engine.js  # محرك الرسم
│       └── main.js        # الوظائف الرئيسية
├── api/                   # واجهات برمجة التطبيقات
│   ├── save_diagram.php   # حفظ المخططات
│   ├── load_diagrams.php  # تحميل قائمة المخططات
│   ├── get_diagram.php    # تحميل مخطط محدد
│   └── upload_image.php   # رفع الصور
├── config/
│   └── database.php       # إعدادات قاعدة البيانات
├── uploads/
│   └── images/           # مجلد الصور المرفوعة
└── README.md             # هذا الملف
```

## التطوير المستقبلي

- [ ] إضافة المزيد من الأشكال الهندسية
- [ ] وظيفة التراجع والإعادة (Undo/Redo)
- [ ] طبقات متعددة للرسم
- [ ] تصدير بصيغ مختلفة (SVG, PDF)
- [ ] مشاركة المخططات مع الآخرين
- [ ] قوالب جاهزة للمخططات الشائعة

## الدعم والمساعدة

هذا التطبيق مصمم للاستخدام الشخصي ويمكن تخصيصه حسب احتياجاتك.

### نصائح للاستخدام الأمثل

1. **استخدم ألوان متناسقة** لجعل المخطط أكثر وضوحاً
2. **اختر أحجام نصوص مناسبة** للقراءة
3. **نظم العناصر** بشكل منطقي ومتسلسل
4. **احفظ نسخ احتياطية** من مخططاتك المهمة

---

**تم تطوير هذا التطبيق خصيصاً لإنشاء مخططات تقنية احترافية مع دعم كامل للغة العربية** 🚀
