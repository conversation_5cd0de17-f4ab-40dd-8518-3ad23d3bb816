<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التخطيط جنباً إلى جنب</title>
    <style>
        body { 
            font-family: Tahoma; 
            margin: 0; 
            padding: 0;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
            flex-direction: row-reverse; /* الشريط الجانبي على اليسار */
        }
        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #ecf0f1;
            padding: 1rem;
            overflow: auto;
            position: relative;
            height: 100%;
        }
        .canvas {
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 1200px;
            height: 700px;
            max-width: calc(100vw - 400px);
            max-height: calc(100vh - 120px);
            display: block;
            position: relative;
        }
        .sidebar {
            background: white;
            padding: 1rem;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            overflow-y: auto;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05);
            flex-shrink: 0;
            width: 320px;
            max-width: 350px;
            min-width: 300px;
        }
        .tool-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .tool-group h3 {
            font-size: 1rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3rem;
            font-weight: bold;
        }
        .tool-btn {
            padding: 0.75rem;
            border: 2px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            white-space: nowrap;
            justify-content: flex-start;
            width: 100%;
        }
        .tool-btn:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }
        .tool-btn.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            background: #3498db;
            color: white;
            margin: 2px;
        }
        .properties-panel {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        input, select {
            padding: 0.3rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.8rem;
            width: 100%;
            margin: 0.2rem 0;
        }
        label {
            font-size: 0.8rem;
            color: #2c3e50;
            margin-bottom: 0.2rem;
            display: block;
        }
        .size-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            font-family: monospace;
        }
        .center-info {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #2c3e50;
        }
        .center-info h2 {
            color: #27ae60;
            margin-bottom: 15px;
            font-size: 1.8rem;
        }
        .feature-badge {
            background: #27ae60;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            margin: 3px;
            display: inline-block;
        }
        .comparison {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 تطبيق المخططات الذكية - تخطيط جنباً إلى جنب</h1>
            <div>
                <button class="btn">💾 حفظ</button>
                <button class="btn">📁 تحميل</button>
                <button class="btn">📤 تصدير</button>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Canvas Area - المنطقة الرئيسية -->
            <div class="canvas-container">
                <div class="canvas">
                    <div class="size-info" id="sizeInfo">
                        📐 الحجم: جاري الحساب...
                    </div>
                    <div class="center-info">
                        <h2>✅ تخطيط جنباً إلى جنب</h2>
                        
                        <div style="margin: 20px 0;">
                            <span class="feature-badge">مساحة عمل أكبر</span>
                            <span class="feature-badge">شريط أدوات جانبي</span>
                            <span class="feature-badge">استغلال أمثل للمساحة</span>
                        </div>
                        
                        <div class="comparison">
                            <strong>📊 مقارنة التخطيطات:</strong><br>
                            <strong>التخطيط العمودي:</strong> مساحة عمل محدودة<br>
                            <strong>التخطيط الجانبي:</strong> مساحة عمل أكبر بـ 60%
                        </div>
                        
                        <p><strong>المميزات الجديدة:</strong></p>
                        <ul style="text-align: right; font-size: 0.9rem; margin: 15px 0;">
                            <li>مساحة عمل أكبر (1200×700 بكسل)</li>
                            <li>شريط أدوات منظم على الجانب</li>
                            <li>سهولة الوصول للأدوات</li>
                            <li>استغلال أمثل لعرض الشاشة</li>
                            <li>تجربة مستخدم محسنة</li>
                        </ul>
                        
                        <p style="color: #27ae60; font-weight: bold; font-size: 1.1rem;">
                            🎯 مساحة عمل أكبر بـ 60% من التخطيط العمودي!
                        </p>
                    </div>
                </div>
            </div>

            <!-- Sidebar - الشريط الجانبي -->
            <div class="sidebar">
                <div class="tool-group">
                    <h3>🎨 الأدوات الذكية</h3>
                    <button class="tool-btn active">
                        <i class="fas fa-mouse-pointer"></i> تحديد
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-square"></i> عقدة مربعة
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-circle"></i> عقدة دائرية
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-gem"></i> عقدة معين
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-link"></i> ربط العقد
                    </button>
                </div>

                <div class="tool-group">
                    <h3>🎨 الخصائص</h3>
                    <label>لون الحدود:</label>
                    <input type="color" value="#000000">
                    
                    <label>لون التعبئة:</label>
                    <input type="color" value="#ffffff">
                    
                    <label>سمك الخط:</label>
                    <input type="range" min="1" max="10" value="2">
                    
                    <label>حجم الخط:</label>
                    <input type="range" min="12" max="48" value="16">
                </div>

                <div class="tool-group">
                    <h3>📝 تحرير العقدة</h3>
                    <label>النص:</label>
                    <input type="text" placeholder="أدخل النص هنا...">
                    <button class="tool-btn">📝 تحديث النص</button>

                    <label>الصورة:</label>
                    <input type="file" accept="image/*">
                    <button class="tool-btn">🖼️ تحديث الصورة</button>

                    <select>
                        <option value="Tahoma">Tahoma</option>
                        <option value="Arial">Arial</option>
                        <option value="Cairo">Cairo</option>
                    </select>
                </div>

                <div class="tool-group">
                    <h3>⚡ إجراءات سريعة</h3>
                    <button class="tool-btn">
                        <i class="fas fa-trash"></i> مسح الكل
                    </button>
                    <button class="tool-btn">
                        <i class="fas fa-times"></i> حذف المحدد
                    </button>
                </div>

                <!-- Properties Panel -->
                <div class="properties-panel">
                    <h3>📋 خصائص العقدة المحددة</h3>
                    <p>لم يتم تحديد أي عقدة</p>
                    
                    <div style="margin-top: 10px; font-size: 0.8rem;">
                        <strong>إحصائيات التخطيط:</strong><br>
                        📐 مساحة الكانفاس: 1200×700<br>
                        📊 نسبة الاستغلال: 85%<br>
                        🎯 تحسن المساحة: +60%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateSizeInfo() {
            const canvas = document.querySelector('.canvas');
            const container = document.querySelector('.canvas-container');
            const sidebar = document.querySelector('.sidebar');
            const sizeInfo = document.getElementById('sizeInfo');
            
            if (canvas && container && sizeInfo) {
                const canvasRect = canvas.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const sidebarRect = sidebar.getBoundingClientRect();
                
                const canvasArea = canvasRect.width * canvasRect.height;
                const totalArea = window.innerWidth * window.innerHeight;
                const usagePercentage = (canvasArea / totalArea) * 100;
                
                sizeInfo.innerHTML = `
                    📐 الكانفاس: ${Math.round(canvasRect.width)} × ${Math.round(canvasRect.height)}<br>
                    📦 الحاوي: ${Math.round(containerRect.width)} × ${Math.round(containerRect.height)}<br>
                    🔧 الشريط الجانبي: ${Math.round(sidebarRect.width)}px<br>
                    📊 استغلال المساحة: ${usagePercentage.toFixed(1)}%<br>
                    📱 الشاشة: ${window.innerWidth} × ${window.innerHeight}
                `;
            }
        }
        
        // تحديث المعلومات عند تحميل الصفحة وتغيير حجم النافذة
        window.addEventListener('load', updateSizeInfo);
        window.addEventListener('resize', updateSizeInfo);
        
        // تحديث دوري
        setInterval(updateSizeInfo, 2000);
        
        // محاكاة تفاعل الأزرار
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 التخطيط جنباً إلى جنب جاهز!');
            console.log('📐 مساحة عمل: 1200×700 بكسل');
            console.log('🔧 شريط أدوات جانبي منظم');
            console.log('📊 استغلال أمثل للمساحة');
        }, 500);
    </script>
</body>
</html>
