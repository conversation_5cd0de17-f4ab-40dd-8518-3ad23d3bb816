/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>homa', 'Arial', sans-serif;
    background: #f5f5f5;
    direction: rtl;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* Header */
.header {
    background: #2c3e50;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

/* Toolbar */
.toolbar {
    background: white;
    padding: 0.5rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    flex-wrap: wrap;
    flex-shrink: 0;
    max-height: 120px;
}

.tool-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    min-width: 120px;
    max-width: 180px;
}

.tool-group h3 {
    font-size: 0.8rem;
    color: #2c3e50;
    margin-bottom: 0.3rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.1rem;
}

.tool-btn {
    padding: 0.4rem;
    border: 2px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.2rem;
    font-size: 0.7rem;
    white-space: nowrap;
}

.tool-btn:hover {
    border-color: #3498db;
    background: #ecf0f1;
}

.tool-btn.active {
    border-color: #3498db;
    background: #3498db;
    color: white;
}

/* Form Controls */
input[type="color"],
input[type="range"],
input[type="text"],
select,
textarea {
    padding: 0.3rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.8rem;
    width: 100%;
}

input[type="range"] {
    margin: 0.2rem 0;
}

label {
    font-size: 0.8rem;
    color: #2c3e50;
    margin-bottom: 0.2rem;
    display: block;
}

/* Canvas Container */
.canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ecf0f1;
    padding: 0.5rem;
    overflow: auto;
    position: relative;
    min-height: calc(100vh - 250px);
    height: auto;
}

#diagramCanvas {
    background: white;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    cursor: crosshair;
    width: 1400px;
    height: 700px;
    max-width: 98vw;
    max-height: calc(100vh - 280px);
    display: block;
}

/* Properties Panel */
.properties-panel {
    background: white;
    padding: 0.5rem;
    border-top: 1px solid #ddd;
    min-height: 60px;
    max-height: 100px;
    overflow-y: auto;
    flex-shrink: 0;
    font-size: 0.8rem;
}

.properties-panel h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    top: 10px;
    left: 15px;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Form Styling */
form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

form label {
    font-weight: bold;
    color: #2c3e50;
}

form input,
form textarea {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

form textarea {
    min-height: 80px;
    resize: vertical;
}

/* Image Preview */
#imagePreview {
    margin-top: 0.5rem;
    max-height: 100px;
    overflow: hidden;
}

#imagePreview img {
    max-width: 100%;
    max-height: 100px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

/* Saved Diagrams */
#savedDiagrams {
    max-height: 300px;
    overflow-y: auto;
}

.diagram-item {
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.diagram-item:hover {
    background: #ecf0f1;
    border-color: #3498db;
}

.diagram-item h4 {
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.diagram-item p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Instructions */
.instructions {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #3498db;
}

.instructions h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.instructions ul {
    margin: 0;
    padding-right: 1rem;
}

.instructions li {
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
    color: #555;
}

.instructions strong {
    color: #2c3e50;
}

/* Node Types */
.tool-btn[data-tool="rectangle"] {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.tool-btn[data-tool="circle"] {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.tool-btn[data-tool="diamond"] {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.tool-btn[data-tool="connect"] {
    background: linear-gradient(45deg, #27ae60, #229954);
    color: white;
}

/* Enhanced Buttons */
.btn {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        height: 100vh;
    }

    .toolbar {
        flex-direction: column;
        gap: 1rem;
        padding: 0.5rem;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        padding: 0.5rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .canvas-container {
        height: calc(100vh - 400px);
        padding: 0.5rem;
    }

    #diagramCanvas {
        width: 100% !important;
        height: 100% !important;
        max-width: 100%;
        max-height: 100%;
    }

    .tool-group {
        min-width: auto;
        flex: 1;
    }

    .properties-panel {
        max-height: 120px;
        overflow-y: auto;
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.2rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .tool-btn {
        padding: 0.4rem;
        font-size: 0.7rem;
    }

    .canvas-container {
        height: calc(100vh - 450px);
        padding: 0.25rem;
    }
}
