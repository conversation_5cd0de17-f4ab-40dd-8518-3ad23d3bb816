<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص المشاكل</title>
    <style>
        body { font-family: Tahoma; padding: 20px; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 تشخيص مشاكل التطبيق</h1>
    <div id="logs"></div>
    
    <script>
        const logs = document.getElementById('logs');
        
        function addLog(message, type = 'log') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        // اختبار تحميل الملفات
        addLog('بدء اختبار تحميل الملفات...', 'log');
        
        // اختبار تحميل CSS
        fetch('/assets/css/style.css')
            .then(response => {
                if (response.ok) {
                    addLog('✅ ملف CSS تم تحميله بنجاح', 'success');
                } else {
                    addLog('❌ فشل في تحميل ملف CSS: ' + response.status, 'error');
                }
            })
            .catch(error => {
                addLog('❌ خطأ في تحميل CSS: ' + error.message, 'error');
            });
        
        // اختبار تحميل JavaScript
        fetch('/assets/js/diagram-engine.js')
            .then(response => {
                if (response.ok) {
                    addLog('✅ ملف diagram-engine.js تم تحميله بنجاح', 'success');
                    return response.text();
                } else {
                    addLog('❌ فشل في تحميل diagram-engine.js: ' + response.status, 'error');
                }
            })
            .then(text => {
                if (text && text.includes('class DiagramEngine')) {
                    addLog('✅ فئة DiagramEngine موجودة في الملف', 'success');
                } else {
                    addLog('⚠️ فئة DiagramEngine غير موجودة أو تالفة', 'warning');
                }
            })
            .catch(error => {
                addLog('❌ خطأ في تحميل diagram-engine.js: ' + error.message, 'error');
            });
        
        fetch('/assets/js/main.js')
            .then(response => {
                if (response.ok) {
                    addLog('✅ ملف main.js تم تحميله بنجاح', 'success');
                } else {
                    addLog('❌ فشل في تحميل main.js: ' + response.status, 'error');
                }
            })
            .catch(error => {
                addLog('❌ خطأ في تحميل main.js: ' + error.message, 'error');
            });
        
        // اختبار Canvas
        setTimeout(() => {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    addLog('✅ Canvas API يعمل بشكل صحيح', 'success');
                } else {
                    addLog('❌ Canvas API لا يعمل', 'error');
                }
            } catch (error) {
                addLog('❌ خطأ في Canvas: ' + error.message, 'error');
            }
        }, 1000);
        
        // اختبار localStorage
        try {
            localStorage.setItem('test', 'value');
            const value = localStorage.getItem('test');
            if (value === 'value') {
                addLog('✅ localStorage يعمل بشكل صحيح', 'success');
                localStorage.removeItem('test');
            } else {
                addLog('❌ localStorage لا يعمل', 'error');
            }
        } catch (error) {
            addLog('❌ خطأ في localStorage: ' + error.message, 'error');
        }
        
        // اختبار الأخطاء في وحدة التحكم
        window.addEventListener('error', function(e) {
            addLog('❌ خطأ JavaScript: ' + e.message + ' في ' + e.filename + ':' + e.lineno, 'error');
        });
        
        // اختبار تحميل الصفحة الرئيسية
        setTimeout(() => {
            addLog('🔗 اختبار الرابط الرئيسي...', 'log');
            addLog('<a href="/" target="_blank">انقر هنا لفتح التطبيق الرئيسي</a>', 'log');
            addLog('<a href="/simple-test.html" target="_blank">انقر هنا لفتح الاختبار البسيط</a>', 'log');
        }, 2000);
    </script>
</body>
</html>
