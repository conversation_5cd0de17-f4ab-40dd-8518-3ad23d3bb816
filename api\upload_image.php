<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('لم يتم رفع الصورة بشكل صحيح');
    }
    
    $file = $_FILES['image'];
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    // التحقق من نوع الملف
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP');
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > $max_size) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5MB');
    }
    
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    $upload_dir = '../uploads/images/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // إنشاء اسم ملف فريد
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $file_path = $upload_dir . $filename;
    
    // نقل الملف
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        throw new Exception('فشل في حفظ الملف');
    }
    
    // حفظ معلومات الملف في قاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        $sql = "INSERT INTO uploaded_images (filename, original_name, file_path, file_size, mime_type) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $filename,
            $file['name'],
            $file_path,
            $file['size'],
            $file['type']
        ]);
    }
    
    // إرجاع معلومات الملف
    echo json_encode([
        'success' => true,
        'message' => 'تم رفع الصورة بنجاح',
        'file_info' => [
            'filename' => $filename,
            'original_name' => $file['name'],
            'url' => 'uploads/images/' . $filename,
            'size' => $file['size'],
            'type' => $file['type']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
