<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق المخططات التقنية</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-project-diagram"></i> تطبيق المخططات التقنية</h1>
            <div class="header-actions">
                <button id="saveBtn" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ المخطط
                </button>
                <button id="loadBtn" class="btn btn-secondary">
                    <i class="fas fa-folder-open"></i> تحميل مخطط
                </button>
                <button id="exportBtn" class="btn btn-success">
                    <i class="fas fa-download"></i> تصدير كصورة
                </button>
            </div>
        </header>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="tool-group">
                <h3>الأدوات</h3>
                <button id="selectTool" class="tool-btn active" data-tool="select">
                    <i class="fas fa-mouse-pointer"></i> تحديد
                </button>
                <button id="rectTool" class="tool-btn" data-tool="rectangle">
                    <i class="fas fa-square"></i> مربع
                </button>
                <button id="circleTool" class="tool-btn" data-tool="circle">
                    <i class="fas fa-circle"></i> دائرة
                </button>
                <button id="arrowTool" class="tool-btn" data-tool="arrow">
                    <i class="fas fa-arrow-right"></i> سهم
                </button>
                <button id="textTool" class="tool-btn" data-tool="text">
                    <i class="fas fa-font"></i> نص
                </button>
                <button id="imageTool" class="tool-btn" data-tool="image">
                    <i class="fas fa-image"></i> صورة
                </button>
            </div>

            <div class="tool-group">
                <h3>الخصائص</h3>
                <label>لون الحدود:</label>
                <input type="color" id="strokeColor" value="#000000">
                
                <label>لون التعبئة:</label>
                <input type="color" id="fillColor" value="#ffffff">
                
                <label>سمك الخط:</label>
                <input type="range" id="strokeWidth" min="1" max="10" value="2">
                
                <label>حجم الخط:</label>
                <input type="range" id="fontSize" min="12" max="48" value="16">
            </div>

            <div class="tool-group">
                <h3>النص</h3>
                <input type="text" id="textInput" placeholder="أدخل النص هنا...">
                <select id="fontFamily">
                    <option value="Arial">Arial</option>
                    <option value="Tahoma">Tahoma</option>
                    <option value="Cairo">Cairo</option>
                </select>
            </div>

            <div class="tool-group">
                <h3>الصور</h3>
                <input type="file" id="imageUpload" accept="image/*">
                <div id="imagePreview"></div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-container">
            <canvas id="diagramCanvas" width="1200" height="800"></canvas>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <h3>خصائص العنصر المحدد</h3>
            <div id="elementProperties">
                <p>لم يتم تحديد أي عنصر</p>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="saveModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>حفظ المخطط</h2>
            <form id="saveForm">
                <label for="diagramName">اسم المخطط:</label>
                <input type="text" id="diagramName" required>
                <label for="diagramDescription">الوصف:</label>
                <textarea id="diagramDescription"></textarea>
                <button type="submit" class="btn btn-primary">حفظ</button>
            </form>
        </div>
    </div>

    <div id="loadModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تحميل مخطط</h2>
            <div id="savedDiagrams">
                <!-- سيتم تحميل المخططات المحفوظة هنا -->
            </div>
        </div>
    </div>

    <script src="assets/js/diagram-engine.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
