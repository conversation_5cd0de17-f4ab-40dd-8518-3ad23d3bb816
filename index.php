<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق المخططات التقنية</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-project-diagram"></i> تطبيق المخططات التقنية</h1>
            <div class="header-actions">
                <button id="saveBtn" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ المخطط
                </button>
                <button id="loadBtn" class="btn btn-secondary">
                    <i class="fas fa-folder-open"></i> تحميل مخطط
                </button>
                <button id="exportBtn" class="btn btn-success">
                    <i class="fas fa-download"></i> تصدير كصورة
                </button>
            </div>
        </header>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="tool-group">
                <h3>الأدوات الذكية</h3>
                <button id="selectTool" class="tool-btn active" data-tool="select">
                    <i class="fas fa-mouse-pointer"></i> تحديد
                </button>
                <button id="rectTool" class="tool-btn" data-tool="rectangle">
                    <i class="fas fa-square"></i> عقدة مربعة
                </button>
                <button id="circleTool" class="tool-btn" data-tool="circle">
                    <i class="fas fa-circle"></i> عقدة دائرية
                </button>
                <button id="diamondTool" class="tool-btn" data-tool="diamond">
                    <i class="fas fa-gem"></i> عقدة معين
                </button>
                <button id="connectTool" class="tool-btn" data-tool="connect">
                    <i class="fas fa-link"></i> ربط العقد
                </button>
            </div>

            <div class="tool-group">
                <h3>الخصائص</h3>
                <label>لون الحدود:</label>
                <input type="color" id="strokeColor" value="#000000">
                
                <label>لون التعبئة:</label>
                <input type="color" id="fillColor" value="#ffffff">
                
                <label>سمك الخط:</label>
                <input type="range" id="strokeWidth" min="1" max="10" value="2">
                
                <label>حجم الخط:</label>
                <input type="range" id="fontSize" min="12" max="48" value="16">
            </div>

            <div class="tool-group">
                <h3>تحرير العقدة المحددة</h3>
                <label>النص:</label>
                <input type="text" id="nodeTextInput" placeholder="أدخل النص هنا...">
                <button id="updateTextBtn" class="btn btn-primary">تحديث النص</button>

                <label>الصورة:</label>
                <input type="file" id="nodeImageUpload" accept="image/*">
                <button id="updateImageBtn" class="btn btn-primary">تحديث الصورة</button>

                <select id="fontFamily">
                    <option value="Tahoma">Tahoma</option>
                    <option value="Arial">Arial</option>
                    <option value="Cairo">Cairo</option>
                </select>
            </div>

            <div class="tool-group">
                <h3>إجراءات سريعة</h3>
                <button id="clearBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i> مسح الكل
                </button>
                <button id="deleteBtn" class="btn btn-warning">
                    <i class="fas fa-times"></i> حذف المحدد
                </button>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-container">
            <canvas id="diagramCanvas" width="1600" height="1000"></canvas>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <h3>خصائص العقدة المحددة</h3>
            <div id="nodeProperties">
                <p>لم يتم تحديد أي عقدة</p>
            </div>

            <div class="instructions">
                <h4>كيفية الاستخدام:</h4>
                <ul>
                    <li><strong>إنشاء عقدة:</strong> اختر شكل العقدة وانقر في أي مكان</li>
                    <li><strong>ربط العقد:</strong> اختر أداة الربط وانقر على عقدتين</li>
                    <li><strong>تحرير النص:</strong> حدد العقدة واكتب النص الجديد</li>
                    <li><strong>إضافة صورة:</strong> حدد العقدة وارفع صورة</li>
                    <li><strong>تحريك العقد:</strong> اسحب العقدة لتحريكها</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="saveModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>حفظ المخطط</h2>
            <form id="saveForm">
                <label for="diagramName">اسم المخطط:</label>
                <input type="text" id="diagramName" required>
                <label for="diagramDescription">الوصف:</label>
                <textarea id="diagramDescription"></textarea>
                <button type="submit" class="btn btn-primary">حفظ</button>
            </form>
        </div>
    </div>

    <div id="loadModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تحميل مخطط</h2>
            <div id="savedDiagrams">
                <!-- سيتم تحميل المخططات المحفوظة هنا -->
            </div>
        </div>
    </div>

    <script src="assets/js/diagram-engine.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
