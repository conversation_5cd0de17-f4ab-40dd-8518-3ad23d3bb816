<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حجم مساحة العمل</title>
    <style>
        body { 
            font-family: Tahoma; 
            margin: 0; 
            padding: 0;
            background: #f5f5f5;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 0.75rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        .toolbar {
            background: white;
            padding: 0.75rem;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            flex-wrap: wrap;
            flex-shrink: 0;
        }
        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #ecf0f1;
            padding: 1rem;
            overflow: auto;
            position: relative;
            min-height: 600px;
            height: auto;
        }
        .canvas {
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 1400px;
            height: 700px;
            max-width: 95vw;
            max-height: 70vh;
            display: block;
            position: relative;
        }
        .properties-panel {
            background: white;
            padding: 0.75rem;
            border-top: 1px solid #ddd;
            min-height: 80px;
            max-height: 150px;
            overflow-y: auto;
            flex-shrink: 0;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            background: #3498db;
            color: white;
            margin: 5px;
        }
        .tool-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 120px;
        }
        .tool-group h3 {
            font-size: 0.8rem;
            color: #2c3e50;
            margin-bottom: 0.3rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.2rem;
        }
        .tool-btn {
            padding: 0.4rem;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.7rem;
        }
        .size-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            font-family: monospace;
        }
        .center-info {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #2c3e50;
        }
        .center-info h2 {
            color: #27ae60;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 اختبار حجم مساحة العمل</h1>
            <div>
                <button class="btn">💾 حفظ</button>
                <button class="btn">📁 تحميل</button>
                <button class="btn">📤 تصدير</button>
            </div>
        </div>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="tool-group">
                <h3>الأدوات</h3>
                <button class="tool-btn">🟦 مربع</button>
                <button class="tool-btn">🔴 دائرة</button>
                <button class="tool-btn">🟨 معين</button>
                <button class="tool-btn">🔗 ربط</button>
            </div>
            
            <div class="tool-group">
                <h3>إجراءات</h3>
                <button class="tool-btn">🗑️ مسح</button>
                <button class="tool-btn">❌ حذف</button>
            </div>
            
            <div class="tool-group">
                <h3>تحرير</h3>
                <input type="text" placeholder="النص..." style="padding: 3px; font-size: 0.7rem;">
                <button class="tool-btn">📝 تحديث</button>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-container">
            <div class="canvas">
                <div class="size-info" id="sizeInfo">
                    📐 الحجم: جاري الحساب...
                </div>
                <div class="center-info">
                    <h2>✅ مساحة العمل الكبيرة</h2>
                    <p><strong>الحجم الجديد:</strong> 1400 × 700 بكسل</p>
                    <p><strong>الحد الأدنى:</strong> 800 × 500 بكسل</p>
                    <p><strong>متجاوب:</strong> يتكيف مع حجم الشاشة</p>
                    <br>
                    <p>🎯 <strong>مساحة كافية لإنشاء مخططات معقدة!</strong></p>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <h3>خصائص العقدة المحددة</h3>
            <div id="nodeProperties">
                <p>✅ تم تحسين حجم مساحة العمل بنجاح!</p>
                <p><strong>التحسينات:</strong></p>
                <ul style="font-size: 0.8rem; margin: 5px 0;">
                    <li>زيادة حجم الكانفاس إلى 1400×700</li>
                    <li>تقليل حجم شريط الأدوات والخصائص</li>
                    <li>تحسين الاستجابة للشاشات المختلفة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function updateSizeInfo() {
            const canvas = document.querySelector('.canvas');
            const container = document.querySelector('.canvas-container');
            const sizeInfo = document.getElementById('sizeInfo');
            
            if (canvas && container && sizeInfo) {
                const canvasRect = canvas.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                
                sizeInfo.innerHTML = `
                    📐 الكانفاس: ${Math.round(canvasRect.width)} × ${Math.round(canvasRect.height)}<br>
                    📦 الحاوي: ${Math.round(containerRect.width)} × ${Math.round(containerRect.height)}<br>
                    📱 الشاشة: ${window.innerWidth} × ${window.innerHeight}<br>
                    🎯 النسبة: ${(canvasRect.width / containerRect.width * 100).toFixed(1)}%
                `;
            }
        }
        
        // تحديث المعلومات عند تحميل الصفحة وتغيير حجم النافذة
        window.addEventListener('load', updateSizeInfo);
        window.addEventListener('resize', updateSizeInfo);
        
        // تحديث دوري
        setInterval(updateSizeInfo, 1000);
        
        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 مساحة العمل الجديدة جاهزة!');
            console.log('📐 الحجم: 1400×700 بكسل');
            console.log('🔧 متجاوب مع جميع الشاشات');
        }, 500);
    </script>
</body>
</html>
