<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور في المخططات</title>
    <style>
        body { 
            font-family: <PERSON>homa; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .canvas-area {
            text-align: center;
            margin: 20px 0;
        }
        canvas {
            border: 2px solid #3498db;
            border-radius: 8px;
            background: white;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            background: #3498db;
            color: white;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.active {
            background: #27ae60;
        }
        .file-input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        .image-preview {
            max-width: 100px;
            max-height: 100px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ اختبار الصور في المخططات</h1>
        
        <div class="test-section">
            <h3>🎨 إنشاء عقدة واختبار الصورة</h3>
            <div class="controls">
                <button id="createNodeBtn" class="btn">🟦 إنشاء عقدة</button>
                <input type="file" id="imageInput" class="file-input" accept="image/*">
                <button id="addImageBtn" class="btn">🖼️ إضافة صورة</button>
                <button id="clearBtn" class="btn">🗑️ مسح الكل</button>
            </div>
            <div id="imagePreview"></div>
        </div>
        
        <div class="canvas-area">
            <canvas id="testCanvas" width="800" height="400"></canvas>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل الأحداث</h3>
            <div id="eventLog" class="log">جاري تحميل اختبار الصور...</div>
            <button onclick="clearLog()" class="btn">🗑️ مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>📖 التعليمات</h3>
            <ol>
                <li><strong>إنشاء عقدة:</strong> انقر على "إنشاء عقدة" لإنشاء عقدة جديدة</li>
                <li><strong>اختيار صورة:</strong> انقر على "اختيار ملف" واختر صورة</li>
                <li><strong>إضافة الصورة:</strong> انقر على "إضافة صورة" لإضافة الصورة للعقدة</li>
                <li><strong>مراقبة السجل:</strong> راقب سجل الأحداث لمتابعة العملية</li>
            </ol>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let canvas, ctx;
        let nodes = [];
        let selectedNode = null;

        // وظيفة تسجيل الأحداث
        function log(message, type = 'info') {
            const logDiv = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
        }

        // تهيئة الكانفاس
        function initCanvas() {
            canvas = document.getElementById('testCanvas');
            ctx = canvas.getContext('2d');
            
            if (!ctx) {
                log('❌ فشل في تهيئة الكانفاس', 'error');
                return false;
            }
            
            log('✅ تم تهيئة الكانفاس بنجاح', 'success');
            return true;
        }

        // إنشاء عقدة جديدة
        function createNode() {
            const node = {
                id: Date.now(),
                x: Math.random() * (canvas.width - 200) + 50,
                y: Math.random() * (canvas.height - 150) + 50,
                width: 200,
                height: 120,
                text: 'عقدة جديدة',
                imageData: null,
                loadedImage: null
            };
            
            nodes.push(node);
            selectedNode = node;
            
            log(`✅ تم إنشاء عقدة جديدة في الموقع (${Math.round(node.x)}, ${Math.round(node.y)})`, 'success');
            render();
        }

        // رسم عقدة
        function drawNode(node) {
            ctx.save();
            
            // رسم خلفية العقدة
            ctx.fillStyle = selectedNode === node ? '#e8f4fd' : '#ecf0f1';
            ctx.strokeStyle = selectedNode === node ? '#3498db' : '#bdc3c7';
            ctx.lineWidth = selectedNode === node ? 3 : 2;
            
            ctx.fillRect(node.x, node.y, node.width, node.height);
            ctx.strokeRect(node.x, node.y, node.width, node.height);
            
            // رسم الصورة إذا كانت موجودة
            if (node.imageData) {
                drawNodeImage(node);
            }
            
            // رسم النص
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Tahoma';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            const textY = node.imageData ? node.y + node.height - 20 : node.y + node.height / 2;
            ctx.fillText(node.text, node.x + node.width / 2, textY);
            
            ctx.restore();
        }

        // رسم صورة العقدة
        function drawNodeImage(node) {
            if (!node.imageData) return;

            // إذا كانت الصورة محملة مسبقاً، ارسمها مباشرة
            if (node.loadedImage) {
                drawLoadedImage(node, node.loadedImage);
                return;
            }

            // تحميل الصورة لأول مرة
            const img = new Image();
            img.onload = () => {
                node.loadedImage = img;
                drawLoadedImage(node, img);
                log('✅ تم تحميل وعرض الصورة', 'success');
                render(); // إعادة رسم الكانفاس
            };
            img.onerror = () => {
                log('❌ فشل في تحميل الصورة', 'error');
            };
            img.src = node.imageData;
        }

        // رسم الصورة المحملة
        function drawLoadedImage(node, img) {
            const imageSize = Math.min(node.width - 20, 80);
            const imageX = node.x + (node.width - imageSize) / 2;
            const imageY = node.y + 10;

            ctx.save();
            
            // رسم خلفية للصورة
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(imageX - 2, imageY - 2, imageSize + 4, imageSize + 4);
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 1;
            ctx.strokeRect(imageX - 2, imageY - 2, imageSize + 4, imageSize + 4);
            
            // قص الصورة في شكل دائري
            ctx.beginPath();
            ctx.arc(imageX + imageSize/2, imageY + imageSize/2, imageSize/2, 0, 2 * Math.PI);
            ctx.clip();
            
            // رسم الصورة
            ctx.drawImage(img, imageX, imageY, imageSize, imageSize);
            
            ctx.restore();
        }

        // رسم كل شيء
        function render() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // رسم خلفية
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // رسم العقد
            nodes.forEach(node => {
                drawNode(node);
            });
            
            // رسم رسالة إذا لم توجد عقد
            if (nodes.length === 0) {
                ctx.fillStyle = '#7f8c8d';
                ctx.font = '16px Tahoma';
                ctx.textAlign = 'center';
                ctx.fillText('انقر على "إنشاء عقدة" لبدء الاختبار', canvas.width / 2, canvas.height / 2);
            }
        }

        // إضافة صورة للعقدة المحددة
        function addImageToNode() {
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];
            
            if (!file) {
                log('⚠️ لم يتم اختيار ملف', 'warning');
                alert('يرجى اختيار صورة أولاً');
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                log('❌ الملف المختار ليس صورة', 'error');
                alert('يرجى اختيار ملف صورة صالح');
                return;
            }
            
            if (!selectedNode) {
                log('⚠️ لم يتم تحديد عقدة', 'warning');
                alert('يرجى إنشاء عقدة أولاً');
                return;
            }

            log(`📁 تحميل الصورة: ${file.name} (${(file.size/1024).toFixed(1)} KB)`, 'info');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                selectedNode.imageData = e.target.result;
                selectedNode.loadedImage = null; // إعادة تعيين الصورة المحملة
                
                // عرض معاينة الصورة
                const preview = document.getElementById('imagePreview');
                preview.innerHTML = `<img src="${e.target.result}" class="image-preview" alt="معاينة الصورة">`;
                
                log('✅ تم تحميل الصورة وإضافتها للعقدة', 'success');
                render();
            };
            reader.onerror = function() {
                log('❌ فشل في قراءة الملف', 'error');
                alert('حدث خطأ في قراءة الملف');
            };
            reader.readAsDataURL(file);
        }

        // مسح جميع العقد
        function clearAll() {
            nodes = [];
            selectedNode = null;
            document.getElementById('imagePreview').innerHTML = '';
            log('🗑️ تم مسح جميع العقد', 'info');
            render();
        }

        // النقر على الكانفاس لتحديد عقدة
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // البحث عن عقدة في الموقع المنقور
            let clickedNode = null;
            for (let node of nodes) {
                if (x >= node.x && x <= node.x + node.width &&
                    y >= node.y && y <= node.y + node.height) {
                    clickedNode = node;
                    break;
                }
            }
            
            if (clickedNode) {
                selectedNode = clickedNode;
                log(`👆 تم تحديد العقدة ${clickedNode.id}`, 'info');
                render();
            }
        });

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار الصور...', 'info');
            
            if (!initCanvas()) {
                return;
            }
            
            // ربط الأزرار
            document.getElementById('createNodeBtn').addEventListener('click', createNode);
            document.getElementById('addImageBtn').addEventListener('click', addImageToNode);
            document.getElementById('clearBtn').addEventListener('click', clearAll);
            
            // رسم أولي
            render();
            
            log('🎉 تم تحميل اختبار الصور بنجاح!', 'success');
            log('💡 نصيحة: أنشئ عقدة أولاً، ثم اختر صورة وأضفها', 'info');
        });
    </script>
</body>
</html>
