<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <style>
        body { 
            font-family: Tahoma; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .tool-btn {
            padding: 0.5rem;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.8rem;
            margin: 5px;
        }
        .tool-btn:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }
        .tool-btn.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            background: #3498db;
            color: white;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار أزرار التطبيق</h1>
        
        <div class="test-section">
            <h3>🔧 أزرار الأدوات</h3>
            <button class="tool-btn active" data-tool="select">👆 تحديد</button>
            <button class="tool-btn" data-tool="rectangle">🟦 عقدة مربعة</button>
            <button class="tool-btn" data-tool="circle">🔴 عقدة دائرية</button>
            <button class="tool-btn" data-tool="diamond">🟨 عقدة معين</button>
            <button class="tool-btn" data-tool="connect">🔗 ربط العقد</button>
        </div>
        
        <div class="test-section">
            <h3>⚡ الإجراءات السريعة</h3>
            <button id="clearBtn" class="btn">🗑️ مسح الكل</button>
            <button id="deleteBtn" class="btn">❌ حذف المحدد</button>
        </div>
        
        <div class="test-section">
            <h3>💾 أزرار الحفظ والتحميل</h3>
            <button id="saveBtn" class="btn">💾 حفظ المخطط</button>
            <button id="loadBtn" class="btn">📁 تحميل مخطط</button>
            <button id="exportBtn" class="btn">📤 تصدير كصورة</button>
        </div>
        
        <div class="test-section">
            <h3>📝 تحرير العقد</h3>
            <input type="text" id="nodeTextInput" placeholder="أدخل النص هنا..." style="padding: 5px; margin: 5px;">
            <button id="updateTextBtn" class="btn">📝 تحديث النص</button>
            <br>
            <input type="file" id="nodeImageUpload" accept="image/*" style="margin: 5px;">
            <button id="updateImageBtn" class="btn">🖼️ تحديث الصورة</button>
        </div>
        
        <div class="test-section">
            <h3>🎨 عناصر التحكم في الخصائص</h3>
            <label>لون الحدود:</label>
            <input type="color" id="strokeColor" value="#2c3e50">
            <label>لون التعبئة:</label>
            <input type="color" id="fillColor" value="#ecf0f1">
            <br>
            <label>سمك الخط:</label>
            <input type="range" id="strokeWidth" min="1" max="10" value="2">
            <label>حجم الخط:</label>
            <input type="range" id="fontSize" min="8" max="32" value="14">
            <br>
            <label>نوع الخط:</label>
            <select id="fontFamily">
                <option value="Tahoma">Tahoma</option>
                <option value="Arial">Arial</option>
                <option value="Verdana">Verdana</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل الأحداث</h3>
            <div id="eventLog" class="log">جاري تحميل الاختبار...</div>
            <button onclick="clearLog()" class="btn">🗑️ مسح السجل</button>
        </div>
    </div>

    <script>
        // محاكاة diagramEngine
        window.diagramEngine = {
            currentTool: 'select',
            selectedNode: null,
            selectedConnection: null,
            clear: function() { log('تم مسح جميع العناصر', 'success'); },
            deleteSelected: function() { log('تم حذف العنصر المحدد', 'success'); },
            updateNodeText: function(node, text) { log('تم تحديث النص: ' + text, 'success'); },
            updateNodeImage: function(node, imageData) { log('تم تحديث الصورة', 'success'); }
        };

        // وظيفة تسجيل الأحداث
        function log(message, type = 'info') {
            const logDiv = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
        }

        // اختبار الأزرار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار الأزرار...', 'info');
            
            // اختبار أزرار الأدوات
            const toolButtons = document.querySelectorAll('.tool-btn');
            log(`📋 تم العثور على ${toolButtons.length} أزرار أدوات`, 'success');
            
            toolButtons.forEach((btn, index) => {
                const tool = btn.getAttribute('data-tool');
                btn.addEventListener('click', function() {
                    log(`🖱️ تم النقر على أداة: ${tool}`, 'success');
                    
                    // إزالة التحديد من جميع الأدوات
                    toolButtons.forEach(b => b.classList.remove('active'));
                    // تحديد الأداة الحالية
                    this.classList.add('active');
                    
                    diagramEngine.currentTool = tool;
                });
                log(`🔘 تم ربط الزر ${index + 1}: ${tool}`, 'info');
            });
            
            // اختبار الإجراءات السريعة
            const clearBtn = document.getElementById('clearBtn');
            const deleteBtn = document.getElementById('deleteBtn');
            
            if (clearBtn) {
                clearBtn.addEventListener('click', function() {
                    log('🖱️ تم النقر على زر مسح الكل', 'info');
                    if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
                        diagramEngine.clear();
                    }
                });
                log('✅ تم ربط زر مسح الكل', 'success');
            }
            
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    log('🖱️ تم النقر على زر حذف المحدد', 'info');
                    diagramEngine.deleteSelected();
                });
                log('✅ تم ربط زر حذف المحدد', 'success');
            }
            
            // اختبار أزرار الحفظ والتحميل
            const saveBtn = document.getElementById('saveBtn');
            const loadBtn = document.getElementById('loadBtn');
            const exportBtn = document.getElementById('exportBtn');
            
            if (saveBtn) {
                saveBtn.addEventListener('click', () => log('🖱️ تم النقر على زر الحفظ', 'success'));
                log('✅ تم ربط زر الحفظ', 'success');
            }
            
            if (loadBtn) {
                loadBtn.addEventListener('click', () => log('🖱️ تم النقر على زر التحميل', 'success'));
                log('✅ تم ربط زر التحميل', 'success');
            }
            
            if (exportBtn) {
                exportBtn.addEventListener('click', () => log('🖱️ تم النقر على زر التصدير', 'success'));
                log('✅ تم ربط زر التصدير', 'success');
            }
            
            // اختبار تحرير العقد
            const updateTextBtn = document.getElementById('updateTextBtn');
            const nodeTextInput = document.getElementById('nodeTextInput');
            const updateImageBtn = document.getElementById('updateImageBtn');
            
            if (updateTextBtn && nodeTextInput) {
                updateTextBtn.addEventListener('click', function() {
                    const text = nodeTextInput.value.trim();
                    log('🖱️ تم النقر على زر تحديث النص', 'info');
                    diagramEngine.updateNodeText(null, text || 'نص جديد');
                });
                log('✅ تم ربط زر تحديث النص', 'success');
            }
            
            if (updateImageBtn) {
                updateImageBtn.addEventListener('click', function() {
                    log('🖱️ تم النقر على زر تحديث الصورة', 'info');
                    diagramEngine.updateNodeImage(null, 'test-image-data');
                });
                log('✅ تم ربط زر تحديث الصورة', 'success');
            }
            
            // اختبار عناصر التحكم في الخصائص
            const controls = ['strokeColor', 'fillColor', 'strokeWidth', 'fontSize', 'fontFamily'];
            controls.forEach(controlId => {
                const element = document.getElementById(controlId);
                if (element) {
                    element.addEventListener('change', () => log(`🎨 تم تغيير ${controlId}`, 'success'));
                    element.addEventListener('input', () => log(`🎨 تم تعديل ${controlId}`, 'info'));
                    log(`✅ تم ربط عنصر التحكم: ${controlId}`, 'success');
                } else {
                    log(`❌ عنصر التحكم غير موجود: ${controlId}`, 'error');
                }
            });
            
            log('🎉 تم الانتهاء من اختبار جميع الأزرار!', 'success');
        });
    </script>
</body>
</html>
