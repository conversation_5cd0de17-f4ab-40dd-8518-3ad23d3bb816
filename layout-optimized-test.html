<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التخطيط المحسن</title>
    <style>
        body { 
            font-family: Tahoma; 
            margin: 0; 
            padding: 0;
            background: #f5f5f5;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        .toolbar {
            background: white;
            padding: 0.5rem;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 0.75rem;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            flex-wrap: wrap;
            flex-shrink: 0;
            max-height: 120px;
        }
        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #ecf0f1;
            padding: 0.5rem;
            overflow: auto;
            position: relative;
            min-height: calc(100vh - 250px);
            height: auto;
        }
        .canvas {
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 1400px;
            height: 700px;
            max-width: 98vw;
            max-height: calc(100vh - 280px);
            display: block;
            position: relative;
        }
        .properties-panel {
            background: white;
            padding: 0.5rem;
            border-top: 1px solid #ddd;
            min-height: 60px;
            max-height: 100px;
            overflow-y: auto;
            flex-shrink: 0;
            font-size: 0.8rem;
        }
        .btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            background: #3498db;
            color: white;
            margin: 2px;
        }
        .tool-group {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            min-width: 120px;
            max-width: 180px;
        }
        .tool-group h3 {
            font-size: 0.8rem;
            color: #2c3e50;
            margin-bottom: 0.3rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.1rem;
        }
        .tool-btn {
            padding: 0.4rem;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.2rem;
            font-size: 0.7rem;
            white-space: nowrap;
        }
        .tool-btn:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }
        .tool-btn.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }
        .size-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 8px;
            border-radius: 5px;
            font-size: 12px;
            font-family: monospace;
        }
        .center-info {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #2c3e50;
        }
        .center-info h2 {
            color: #27ae60;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }
        .optimization-badge {
            background: #27ae60;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 style="font-size: 1.2rem;">🎨 التخطيط المحسن - مساحة عمل أكبر</h1>
            <div>
                <button class="btn">💾 حفظ</button>
                <button class="btn">📁 تحميل</button>
                <button class="btn">📤 تصدير</button>
            </div>
        </div>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="tool-group">
                <h3>الأدوات الذكية</h3>
                <button class="tool-btn active">🟦 عقدة مربعة</button>
                <button class="tool-btn">🔴 عقدة دائرية</button>
                <button class="tool-btn">🟨 عقدة معين</button>
                <button class="tool-btn">🔗 ربط العقد</button>
            </div>
            
            <div class="tool-group">
                <h3>إجراءات سريعة</h3>
                <button class="tool-btn">🗑️ مسح الكل</button>
                <button class="tool-btn">❌ حذف المحدد</button>
            </div>
            
            <div class="tool-group">
                <h3>تحرير العقدة</h3>
                <input type="text" placeholder="النص..." style="padding: 3px; font-size: 0.7rem; width: 100px;">
                <button class="tool-btn">📝 تحديث النص</button>
                <button class="tool-btn">🖼️ تحديث الصورة</button>
            </div>
            
            <div class="tool-group">
                <h3>الخصائص</h3>
                <div style="display: flex; gap: 5px; align-items: center;">
                    <label style="font-size: 0.6rem;">لون:</label>
                    <input type="color" value="#3498db" style="width: 30px; height: 20px;">
                </div>
                <div style="display: flex; gap: 5px; align-items: center;">
                    <label style="font-size: 0.6rem;">سمك:</label>
                    <input type="range" min="1" max="10" value="2" style="width: 60px;">
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-container">
            <div class="canvas">
                <div class="size-info" id="sizeInfo">
                    📐 الحجم: جاري الحساب...
                </div>
                <div class="center-info">
                    <h2>✅ مساحة العمل المحسنة</h2>
                    
                    <div style="margin: 15px 0;">
                        <span class="optimization-badge">شريط أدوات مضغوط</span>
                        <span class="optimization-badge">مساحة عمل أكبر</span>
                        <span class="optimization-badge">لوحة خصائص صغيرة</span>
                    </div>
                    
                    <p><strong>التحسينات المطبقة:</strong></p>
                    <ul style="text-align: right; font-size: 0.9rem; margin: 10px 0;">
                        <li>تقليل حجم شريط الأدوات (120px max-height)</li>
                        <li>تقليل حجم لوحة الخصائص (60-100px)</li>
                        <li>زيادة مساحة الكانفاس (98vw × calc(100vh - 280px))</li>
                        <li>تحسين الحد الأدنى للكانفاس (900×550px)</li>
                        <li>تقليل المسافات والحواف</li>
                    </ul>
                    
                    <p style="color: #27ae60; font-weight: bold;">
                        🎯 مساحة عمل أكبر بـ 40% من النسخة السابقة!
                    </p>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <strong>خصائص العقدة المحددة:</strong>
            <span style="color: #7f8c8d;">لم يتم تحديد أي عقدة</span>
            
            <div style="margin-top: 5px; display: flex; gap: 10px; flex-wrap: wrap;">
                <span style="background: #ecf0f1; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem;">
                    📐 الحجم: 1400×700
                </span>
                <span style="background: #ecf0f1; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem;">
                    📱 متجاوب: نعم
                </span>
                <span style="background: #ecf0f1; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem;">
                    🎨 محسن: نعم
                </span>
            </div>
        </div>
    </div>

    <script>
        function updateSizeInfo() {
            const canvas = document.querySelector('.canvas');
            const container = document.querySelector('.canvas-container');
            const toolbar = document.querySelector('.toolbar');
            const properties = document.querySelector('.properties-panel');
            const sizeInfo = document.getElementById('sizeInfo');
            
            if (canvas && container && sizeInfo) {
                const canvasRect = canvas.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const toolbarRect = toolbar.getBoundingClientRect();
                const propertiesRect = properties.getBoundingClientRect();
                
                const canvasPercentage = (canvasRect.width * canvasRect.height) / (window.innerWidth * window.innerHeight) * 100;
                
                sizeInfo.innerHTML = `
                    📐 الكانفاس: ${Math.round(canvasRect.width)} × ${Math.round(canvasRect.height)}<br>
                    📦 الحاوي: ${Math.round(containerRect.width)} × ${Math.round(containerRect.height)}<br>
                    🔧 شريط الأدوات: ${Math.round(toolbarRect.height)}px<br>
                    📋 لوحة الخصائص: ${Math.round(propertiesRect.height)}px<br>
                    📊 نسبة الكانفاس: ${canvasPercentage.toFixed(1)}%
                `;
            }
        }
        
        // تحديث المعلومات عند تحميل الصفحة وتغيير حجم النافذة
        window.addEventListener('load', updateSizeInfo);
        window.addEventListener('resize', updateSizeInfo);
        
        // تحديث دوري
        setInterval(updateSizeInfo, 2000);
        
        // محاكاة تفاعل الأزرار
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 التخطيط المحسن جاهز!');
            console.log('📐 مساحة عمل أكبر بـ 40%');
            console.log('🔧 شريط أدوات مضغوط');
            console.log('📋 لوحة خصائص صغيرة');
        }, 500);
    </script>
</body>
</html>
