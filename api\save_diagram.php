<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    $name = trim($input['name'] ?? '');
    $description = trim($input['description'] ?? '');
    $diagram_data = $input['diagram_data'] ?? null;
    
    if (empty($name)) {
        throw new Exception('اسم المخطط مطلوب');
    }
    
    if (!$diagram_data) {
        throw new Exception('بيانات المخطط مطلوبة');
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('خطأ في الاتصال بقاعدة البيانات');
    }
    
    $sql = "INSERT INTO diagrams (name, description, diagram_data) VALUES (?, ?, ?)";
    $stmt = $db->prepare($sql);
    
    $diagram_json = json_encode($diagram_data);
    
    if ($stmt->execute([$name, $description, $diagram_json])) {
        $diagram_id = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ المخطط بنجاح',
            'diagram_id' => $diagram_id
        ]);
    } else {
        throw new Exception('فشل في حفظ المخطط');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
