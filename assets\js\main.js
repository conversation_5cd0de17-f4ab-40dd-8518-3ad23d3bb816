// التطبيق الرئيسي للمخططات الذكية
let diagramEngine;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل تطبيق المخططات الذكية...');

    // التحقق من وجود الكانفاس
    const canvas = document.getElementById('diagramCanvas');
    if (!canvas) {
        console.error('❌ لم يتم العثور على عنصر الكانفاس!');
        alert('خطأ: لم يتم العثور على مساحة الرسم!');
        return;
    }

    try {
        // تهيئة محرك الرسم الذكي
        console.log('🎨 تهيئة محرك الرسم...');
        diagramEngine = new DiagramEngine('diagramCanvas');
        console.log('✅ تم تهيئة محرك الرسم بنجاح');

        // ربط الأدوات
        setupToolbar();
        setupModals();
        setupNodeEditing();
        setupPropertyControls();
        setupQuickActions();
        setupResponsiveCanvas();

        console.log('🎉 تم تحميل تطبيق المخططات الذكية بنجاح');

        // إضافة رسالة للمستخدم
        setTimeout(() => {
            console.log('💡 نصيحة: افتح وحدة التحكم (F12) لرؤية رسائل التشخيص');
        }, 1000);

    } catch (error) {
        console.error('❌ خطأ في تهيئة التطبيق:', error);
        alert('حدث خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
    }
});

// إعداد الكانفاس المتجاوب
function setupResponsiveCanvas() {
    const canvas = document.getElementById('diagramCanvas');
    const container = document.querySelector('.canvas-container');

    function resizeCanvas() {
        const containerRect = container.getBoundingClientRect();
        const maxWidth = containerRect.width - 40; // مع الحواف
        const maxHeight = containerRect.height - 40;

        // تحديد الحجم المناسب - مساحة أكبر
        let canvasWidth = Math.min(1400, maxWidth);
        let canvasHeight = Math.min(700, maxHeight);

        // التأكد من الحد الأدنى للحجم
        canvasWidth = Math.max(800, canvasWidth);
        canvasHeight = Math.max(500, canvasHeight);

        // التأكد من النسبة المناسبة
        if (canvasWidth / canvasHeight > 2.2) {
            canvasHeight = canvasWidth / 2;
        }

        canvas.style.width = canvasWidth + 'px';
        canvas.style.height = canvasHeight + 'px';

        console.log(`📐 تم تحديد حجم الكانفاس: ${canvasWidth} × ${canvasHeight}`);
    }

    // تطبيق التغيير عند تحميل الصفحة وتغيير حجم النافذة
    setTimeout(resizeCanvas, 100); // تأخير قصير للتأكد من تحميل العناصر
    window.addEventListener('resize', resizeCanvas);
}

function setupToolbar() {
    console.log('🔧 إعداد شريط الأدوات...');

    // أدوات الرسم
    const toolButtons = document.querySelectorAll('.tool-btn');
    console.log(`📋 تم العثور على ${toolButtons.length} أزرار أدوات`);

    if (toolButtons.length === 0) {
        console.error('❌ لم يتم العثور على أي أزرار أدوات!');
        return;
    }

    toolButtons.forEach((btn, index) => {
        const tool = btn.getAttribute('data-tool');
        console.log(`🔘 ربط الزر ${index + 1}: ${tool}`);

        btn.addEventListener('click', function() {
            console.log(`🖱️ تم النقر على أداة: ${tool}`);

            // إزالة التحديد من جميع الأدوات
            toolButtons.forEach(b => b.classList.remove('active'));
            // تحديد الأداة الحالية
            this.classList.add('active');

            if (diagramEngine) {
                diagramEngine.currentTool = tool;
                console.log(`✅ تم تعيين الأداة الحالية: ${tool}`);
            } else {
                console.error('❌ diagramEngine غير متاح!');
            }

            // تغيير شكل المؤشر
            const canvas = document.getElementById('diagramCanvas');
            if (canvas) {
                switch(tool) {
                    case 'select':
                        canvas.style.cursor = 'default';
                        break;
                    case 'connect':
                        canvas.style.cursor = 'crosshair';
                        break;
                    default:
                        canvas.style.cursor = 'copy';
                }
                console.log(`🖱️ تم تغيير شكل المؤشر للأداة: ${tool}`);
            }
        });
    });

    // أزرار الحفظ والتحميل والتصدير
    setupHeaderButtons();
}

function setupHeaderButtons() {
    console.log('🔧 إعداد أزرار الرأس...');

    // التحقق من وجود الأزرار
    const saveBtn = document.getElementById('saveBtn');
    const loadBtn = document.getElementById('loadBtn');
    const exportBtn = document.getElementById('exportBtn');

    if (saveBtn) {
        saveBtn.addEventListener('click', showSaveModal);
        console.log('✅ تم ربط زر الحفظ');
    } else {
        console.warn('⚠️ زر الحفظ غير موجود');
    }

    if (loadBtn) {
        loadBtn.addEventListener('click', showLoadModal);
        console.log('✅ تم ربط زر التحميل');
    } else {
        console.warn('⚠️ زر التحميل غير موجود');
    }

    if (exportBtn) {
        exportBtn.addEventListener('click', exportDiagram);
        console.log('✅ تم ربط زر التصدير');
    } else {
        console.warn('⚠️ زر التصدير غير موجود');
    }
}

function setupPropertyControls() {
    console.log('🎨 إعداد عناصر التحكم في الخصائص...');

    // ألوان وخصائص الرسم
    const strokeColor = document.getElementById('strokeColor');
    const fillColor = document.getElementById('fillColor');
    const strokeWidth = document.getElementById('strokeWidth');
    const fontSize = document.getElementById('fontSize');
    const fontFamily = document.getElementById('fontFamily');

    if (strokeColor) {
        strokeColor.addEventListener('change', function() {
            diagramEngine.strokeColor = this.value;
            updateSelectedNodeProperty('strokeColor', this.value);
        });
        console.log('✅ تم ربط عنصر لون الحدود');
    } else {
        console.warn('⚠️ عنصر لون الحدود غير موجود');
    }

    if (fillColor) {
        fillColor.addEventListener('change', function() {
            diagramEngine.fillColor = this.value;
            updateSelectedNodeProperty('fillColor', this.value);
        });
        console.log('✅ تم ربط عنصر لون التعبئة');
    } else {
        console.warn('⚠️ عنصر لون التعبئة غير موجود');
    }

    if (strokeWidth) {
        strokeWidth.addEventListener('input', function() {
            diagramEngine.strokeWidth = parseInt(this.value);
            updateSelectedNodeProperty('strokeWidth', parseInt(this.value));
        });
        console.log('✅ تم ربط عنصر سمك الخط');
    } else {
        console.warn('⚠️ عنصر سمك الخط غير موجود');
    }

    if (fontSize) {
        fontSize.addEventListener('input', function() {
            diagramEngine.fontSize = parseInt(this.value);
            updateSelectedNodeProperty('fontSize', parseInt(this.value));
        });
        console.log('✅ تم ربط عنصر حجم الخط');
    } else {
        console.warn('⚠️ عنصر حجم الخط غير موجود');
    }

    if (fontFamily) {
        fontFamily.addEventListener('change', function() {
            diagramEngine.fontFamily = this.value;
            updateSelectedNodeProperty('fontFamily', this.value);
        });
        console.log('✅ تم ربط عنصر نوع الخط');
    } else {
        console.warn('⚠️ عنصر نوع الخط غير موجود');
    }
}

function updateSelectedNodeProperty(property, value) {
    if (diagramEngine.selectedNode) {
        diagramEngine.selectedNode[property] = value;
        diagramEngine.render();
        updatePropertiesPanel();
    }
}

function setupNodeEditing() {
    console.log('📝 إعداد تحرير العقد...');

    // تحديث النص
    const updateTextBtn = document.getElementById('updateTextBtn');
    const nodeTextInput = document.getElementById('nodeTextInput');
    const updateImageBtn = document.getElementById('updateImageBtn');
    const nodeImageUpload = document.getElementById('nodeImageUpload');

    if (updateTextBtn && nodeTextInput) {
        updateTextBtn.addEventListener('click', function() {
            const text = nodeTextInput.value.trim();
            if (diagramEngine.selectedNode) {
                diagramEngine.updateNodeText(diagramEngine.selectedNode, text || 'نص جديد');
                updatePropertiesPanel();
                console.log('✅ تم تحديث النص');
            }
        });

        // تحديث النص أثناء الكتابة
        nodeTextInput.addEventListener('input', function() {
            const text = this.value.trim();
            if (diagramEngine.selectedNode) {
                diagramEngine.updateNodeText(diagramEngine.selectedNode, text || 'نص جديد');
            }
        });

        // تحديث النص عند الضغط على Enter
        nodeTextInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                updateTextBtn.click();
            }
        });

        console.log('✅ تم ربط عناصر تحرير النص');
    } else {
        console.warn('⚠️ عناصر تحرير النص غير موجودة');
    }

    // تحديث الصورة
    if (updateImageBtn && nodeImageUpload) {
        updateImageBtn.addEventListener('click', function() {
            const file = nodeImageUpload.files[0];

            if (file && file.type.startsWith('image/') && diagramEngine.selectedNode) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    diagramEngine.updateNodeImage(diagramEngine.selectedNode, e.target.result);
                    updatePropertiesPanel();
                    console.log('✅ تم تحديث الصورة');
                };
                reader.readAsDataURL(file);
            }
        });

        console.log('✅ تم ربط عناصر تحرير الصورة');
    } else {
        console.warn('⚠️ عناصر تحرير الصورة غير موجودة');
    }
}

function setupQuickActions() {
    console.log('⚡ إعداد الإجراءات السريعة...');

    // مسح الكل
    const clearBtn = document.getElementById('clearBtn');
    const deleteBtn = document.getElementById('deleteBtn');

    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
                diagramEngine.clear();
                updatePropertiesPanel();
                console.log('✅ تم مسح جميع العناصر');
            }
        });
        console.log('✅ تم ربط زر مسح الكل');
    } else {
        console.warn('⚠️ زر مسح الكل غير موجود');
    }

    // حذف المحدد
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (diagramEngine.selectedNode || diagramEngine.selectedConnection) {
                diagramEngine.deleteSelected();
                updatePropertiesPanel();
                console.log('✅ تم حذف العنصر المحدد');
            }
        });
        console.log('✅ تم ربط زر حذف المحدد');
    } else {
        console.warn('⚠️ زر حذف المحدد غير موجود');
    }
}

function setupModals() {
    // إعداد النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');
    
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    // نموذج الحفظ
    document.getElementById('saveForm').addEventListener('submit', saveDiagram);
}

function showSaveModal() {
    document.getElementById('saveModal').style.display = 'block';
}

function showLoadModal() {
    loadSavedDiagrams();
    document.getElementById('loadModal').style.display = 'block';
}

function saveDiagram(e) {
    e.preventDefault();
    
    const name = document.getElementById('diagramName').value;
    const description = document.getElementById('diagramDescription').value;
    
    const diagramData = {
        name: name,
        description: description,
        data: diagramEngine.getDiagramData(),
        timestamp: new Date().toISOString()
    };
    
    // حفظ في localStorage (يمكن تطويره لاحقاً لحفظ في قاعدة البيانات)
    let savedDiagrams = JSON.parse(localStorage.getItem('savedDiagrams') || '[]');
    savedDiagrams.push(diagramData);
    localStorage.setItem('savedDiagrams', JSON.stringify(savedDiagrams));
    
    // إغلاق النافذة وإظهار رسالة نجاح
    document.getElementById('saveModal').style.display = 'none';
    alert('تم حفظ المخطط بنجاح!');
    
    // مسح النموذج
    document.getElementById('saveForm').reset();
}

function loadSavedDiagrams() {
    const savedDiagrams = JSON.parse(localStorage.getItem('savedDiagrams') || '[]');
    const container = document.getElementById('savedDiagrams');
    
    if (savedDiagrams.length === 0) {
        container.innerHTML = '<p>لا توجد مخططات محفوظة</p>';
        return;
    }
    
    container.innerHTML = '';
    savedDiagrams.forEach((diagram, index) => {
        const diagramItem = document.createElement('div');
        diagramItem.className = 'diagram-item';
        diagramItem.innerHTML = `
            <h4>${diagram.name}</h4>
            <p>${diagram.description || 'بدون وصف'}</p>
            <small>تاريخ الحفظ: ${new Date(diagram.timestamp).toLocaleDateString('ar-SA')}</small>
        `;
        
        diagramItem.addEventListener('click', function() {
            loadDiagram(diagram.data);
            document.getElementById('loadModal').style.display = 'none';
        });
        
        container.appendChild(diagramItem);
    });
}

function loadDiagram(data) {
    diagramEngine.loadDiagram(data);
    alert('تم تحميل المخطط بنجاح!');
}

function exportDiagram() {
    const imageData = diagramEngine.exportAsImage();
    
    // إنشاء رابط تحميل
    const link = document.createElement('a');
    link.download = 'diagram_' + new Date().getTime() + '.png';
    link.href = imageData;
    link.click();
}

function updatePropertiesPanel() {
    const panel = document.getElementById('nodeProperties');

    if (!diagramEngine.selectedNode && !diagramEngine.selectedConnection) {
        panel.innerHTML = '<p>لم يتم تحديد أي عقدة أو رابط</p>';
        // مسح حقول التحرير
        document.getElementById('nodeTextInput').value = '';
        return;
    }

    if (diagramEngine.selectedNode) {
        const node = diagramEngine.selectedNode;
        let html = `<h4>خصائص العقدة ${getNodeTypeName(node.shape)}</h4>`;

        html += `<p><strong>الموقع:</strong> (${Math.round(node.x)}, ${Math.round(node.y)})</p>`;
        html += `<p><strong>الحجم:</strong> ${Math.round(node.width)} × ${Math.round(node.height)}</p>`;
        html += `<p><strong>النص:</strong> ${node.text}</p>`;
        html += `<p><strong>الصورة:</strong> ${node.imageData ? 'موجودة' : 'غير موجودة'}</p>`;

        // تحديث حقل النص
        document.getElementById('nodeTextInput').value = node.text;

        panel.innerHTML = html;
    } else if (diagramEngine.selectedConnection) {
        const connection = diagramEngine.selectedConnection;
        let html = `<h4>خصائص الرابط</h4>`;

        html += `<p><strong>من:</strong> العقدة ${connection.fromNode.text}</p>`;
        html += `<p><strong>إلى:</strong> العقدة ${connection.toNode.text}</p>`;
        html += `<p><strong>سمك الخط:</strong> ${connection.strokeWidth}</p>`;

        panel.innerHTML = html;
    }
}

function getNodeTypeName(shape) {
    const types = {
        'rectangle': 'المربعة',
        'circle': 'الدائرية',
        'diamond': 'المعين'
    };
    return types[shape] || shape;
}

// تحديث لوحة الخصائص عند تغيير التحديد
setInterval(() => {
    updatePropertiesPanel();
}, 500);
