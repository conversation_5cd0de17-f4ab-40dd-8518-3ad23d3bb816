// التطبيق الرئيسي
let diagramEngine;

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة محرك الرسم
    diagramEngine = new DiagramEngine('diagramCanvas');
    
    // ربط الأدوات
    setupToolbar();
    setupModals();
    setupImageUpload();
    setupPropertyControls();
    
    console.log('تم تحميل تطبيق المخططات بنجاح');
});

function setupToolbar() {
    // أدوات الرسم
    const toolButtons = document.querySelectorAll('.tool-btn');
    toolButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // إزالة التحديد من جميع الأدوات
            toolButtons.forEach(b => b.classList.remove('active'));
            // تحديد الأداة الحالية
            this.classList.add('active');
            
            const tool = this.getAttribute('data-tool');
            diagramEngine.currentTool = tool;
            
            // تغيير شكل المؤشر
            const canvas = document.getElementById('diagramCanvas');
            switch(tool) {
                case 'select':
                    canvas.style.cursor = 'default';
                    break;
                case 'text':
                    canvas.style.cursor = 'text';
                    break;
                default:
                    canvas.style.cursor = 'crosshair';
            }
        });
    });
    
    // أزرار الحفظ والتحميل والتصدير
    document.getElementById('saveBtn').addEventListener('click', showSaveModal);
    document.getElementById('loadBtn').addEventListener('click', showLoadModal);
    document.getElementById('exportBtn').addEventListener('click', exportDiagram);
}

function setupPropertyControls() {
    // ألوان وخصائص الرسم
    document.getElementById('strokeColor').addEventListener('change', function() {
        diagramEngine.strokeColor = this.value;
        updateSelectedElementProperty('strokeColor', this.value);
    });
    
    document.getElementById('fillColor').addEventListener('change', function() {
        diagramEngine.fillColor = this.value;
        updateSelectedElementProperty('fillColor', this.value);
    });
    
    document.getElementById('strokeWidth').addEventListener('input', function() {
        diagramEngine.strokeWidth = parseInt(this.value);
        updateSelectedElementProperty('strokeWidth', parseInt(this.value));
    });
    
    document.getElementById('fontSize').addEventListener('input', function() {
        diagramEngine.fontSize = parseInt(this.value);
        updateSelectedElementProperty('fontSize', parseInt(this.value));
    });
    
    document.getElementById('fontFamily').addEventListener('change', function() {
        diagramEngine.fontFamily = this.value;
        updateSelectedElementProperty('fontFamily', this.value);
    });
    
    // إدخال النص
    document.getElementById('textInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const text = this.value.trim();
            if (text) {
                // إضافة النص في وسط الكانفاس
                const canvas = document.getElementById('diagramCanvas');
                diagramEngine.addTextElement(canvas.width / 2, canvas.height / 2, text);
                this.value = '';
            }
        }
    });
}

function updateSelectedElementProperty(property, value) {
    if (diagramEngine.selectedElement) {
        diagramEngine.selectedElement[property] = value;
        diagramEngine.render();
        updatePropertiesPanel();
    }
}

function setupImageUpload() {
    const imageUpload = document.getElementById('imageUpload');
    const imagePreview = document.getElementById('imagePreview');
    
    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = e.target.result;
                
                // عرض معاينة الصورة
                imagePreview.innerHTML = `<img src="${imageData}" alt="معاينة الصورة">`;
                
                // إضافة الصورة للكانفاس
                const canvas = document.getElementById('diagramCanvas');
                diagramEngine.addImageElement(50, 50, imageData);
            };
            reader.readAsDataURL(file);
        }
    });
}

function setupModals() {
    // إعداد النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');
    
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    // نموذج الحفظ
    document.getElementById('saveForm').addEventListener('submit', saveDiagram);
}

function showSaveModal() {
    document.getElementById('saveModal').style.display = 'block';
}

function showLoadModal() {
    loadSavedDiagrams();
    document.getElementById('loadModal').style.display = 'block';
}

function saveDiagram(e) {
    e.preventDefault();
    
    const name = document.getElementById('diagramName').value;
    const description = document.getElementById('diagramDescription').value;
    
    const diagramData = {
        name: name,
        description: description,
        data: diagramEngine.getDiagramData(),
        timestamp: new Date().toISOString()
    };
    
    // حفظ في localStorage (يمكن تطويره لاحقاً لحفظ في قاعدة البيانات)
    let savedDiagrams = JSON.parse(localStorage.getItem('savedDiagrams') || '[]');
    savedDiagrams.push(diagramData);
    localStorage.setItem('savedDiagrams', JSON.stringify(savedDiagrams));
    
    // إغلاق النافذة وإظهار رسالة نجاح
    document.getElementById('saveModal').style.display = 'none';
    alert('تم حفظ المخطط بنجاح!');
    
    // مسح النموذج
    document.getElementById('saveForm').reset();
}

function loadSavedDiagrams() {
    const savedDiagrams = JSON.parse(localStorage.getItem('savedDiagrams') || '[]');
    const container = document.getElementById('savedDiagrams');
    
    if (savedDiagrams.length === 0) {
        container.innerHTML = '<p>لا توجد مخططات محفوظة</p>';
        return;
    }
    
    container.innerHTML = '';
    savedDiagrams.forEach((diagram, index) => {
        const diagramItem = document.createElement('div');
        diagramItem.className = 'diagram-item';
        diagramItem.innerHTML = `
            <h4>${diagram.name}</h4>
            <p>${diagram.description || 'بدون وصف'}</p>
            <small>تاريخ الحفظ: ${new Date(diagram.timestamp).toLocaleDateString('ar-SA')}</small>
        `;
        
        diagramItem.addEventListener('click', function() {
            loadDiagram(diagram.data);
            document.getElementById('loadModal').style.display = 'none';
        });
        
        container.appendChild(diagramItem);
    });
}

function loadDiagram(data) {
    diagramEngine.loadDiagram(data);
    alert('تم تحميل المخطط بنجاح!');
}

function exportDiagram() {
    const imageData = diagramEngine.exportAsImage();
    
    // إنشاء رابط تحميل
    const link = document.createElement('a');
    link.download = 'diagram_' + new Date().getTime() + '.png';
    link.href = imageData;
    link.click();
}

function updatePropertiesPanel() {
    const panel = document.getElementById('elementProperties');
    
    if (!diagramEngine.selectedElement) {
        panel.innerHTML = '<p>لم يتم تحديد أي عنصر</p>';
        return;
    }
    
    const element = diagramEngine.selectedElement;
    let html = `<h4>خصائص ${getElementTypeName(element.type)}</h4>`;
    
    html += `<p><strong>الموقع:</strong> (${Math.round(element.x)}, ${Math.round(element.y)})</p>`;
    
    if (element.width && element.height) {
        html += `<p><strong>الحجم:</strong> ${Math.round(element.width)} × ${Math.round(element.height)}</p>`;
    }
    
    if (element.text) {
        html += `<p><strong>النص:</strong> ${element.text}</p>`;
    }
    
    html += `<button onclick="diagramEngine.deleteSelected()" class="btn btn-danger" style="margin-top: 10px;">
                <i class="fas fa-trash"></i> حذف العنصر
             </button>`;
    
    panel.innerHTML = html;
}

function getElementTypeName(type) {
    const types = {
        'rectangle': 'المربع',
        'circle': 'الدائرة',
        'arrow': 'السهم',
        'text': 'النص',
        'image': 'الصورة'
    };
    return types[type] || type;
}

// تحديث لوحة الخصائص عند تغيير التحديد
setInterval(() => {
    updatePropertiesPanel();
}, 500);
