// التطبيق الرئيسي للمخططات الذكية
let diagramEngine;

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة محرك الرسم الذكي
    diagramEngine = new DiagramEngine('diagramCanvas');

    // ربط الأدوات
    setupToolbar();
    setupModals();
    setupNodeEditing();
    setupPropertyControls();
    setupQuickActions();
    setupResponsiveCanvas();

    console.log('تم تحميل تطبيق المخططات الذكية بنجاح');
});

// إعداد الكانفاس المتجاوب
function setupResponsiveCanvas() {
    const canvas = document.getElementById('diagramCanvas');
    const container = document.querySelector('.canvas-container');

    function resizeCanvas() {
        const containerRect = container.getBoundingClientRect();
        const maxWidth = containerRect.width - 40; // مع الحواف
        const maxHeight = containerRect.height - 40;

        // تحديد الحجم المناسب
        let canvasWidth = Math.min(1200, maxWidth);
        let canvasHeight = Math.min(600, maxHeight);

        // التأكد من النسبة المناسبة
        if (canvasWidth / canvasHeight > 2) {
            canvasHeight = canvasWidth / 2;
        }

        canvas.style.width = canvasWidth + 'px';
        canvas.style.height = canvasHeight + 'px';
    }

    // تطبيق التغيير عند تحميل الصفحة وتغيير حجم النافذة
    setTimeout(resizeCanvas, 100); // تأخير قصير للتأكد من تحميل العناصر
    window.addEventListener('resize', resizeCanvas);
}

function setupToolbar() {
    // أدوات الرسم
    const toolButtons = document.querySelectorAll('.tool-btn');
    toolButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // إزالة التحديد من جميع الأدوات
            toolButtons.forEach(b => b.classList.remove('active'));
            // تحديد الأداة الحالية
            this.classList.add('active');
            
            const tool = this.getAttribute('data-tool');
            diagramEngine.currentTool = tool;
            
            // تغيير شكل المؤشر
            const canvas = document.getElementById('diagramCanvas');
            switch(tool) {
                case 'select':
                    canvas.style.cursor = 'default';
                    break;
                case 'connect':
                    canvas.style.cursor = 'crosshair';
                    break;
                default:
                    canvas.style.cursor = 'copy';
            }
        });
    });
    
    // أزرار الحفظ والتحميل والتصدير
    document.getElementById('saveBtn').addEventListener('click', showSaveModal);
    document.getElementById('loadBtn').addEventListener('click', showLoadModal);
    document.getElementById('exportBtn').addEventListener('click', exportDiagram);
}

function setupPropertyControls() {
    // ألوان وخصائص الرسم
    document.getElementById('strokeColor').addEventListener('change', function() {
        diagramEngine.strokeColor = this.value;
        updateSelectedNodeProperty('strokeColor', this.value);
    });
    
    document.getElementById('fillColor').addEventListener('change', function() {
        diagramEngine.fillColor = this.value;
        updateSelectedNodeProperty('fillColor', this.value);
    });
    
    document.getElementById('strokeWidth').addEventListener('input', function() {
        diagramEngine.strokeWidth = parseInt(this.value);
        updateSelectedNodeProperty('strokeWidth', parseInt(this.value));
    });
    
    document.getElementById('fontSize').addEventListener('input', function() {
        diagramEngine.fontSize = parseInt(this.value);
        updateSelectedNodeProperty('fontSize', parseInt(this.value));
    });
    
    document.getElementById('fontFamily').addEventListener('change', function() {
        diagramEngine.fontFamily = this.value;
        updateSelectedNodeProperty('fontFamily', this.value);
    });
    
    // هذا الكود لم يعد مستخدم في النظام الجديد
}

function updateSelectedNodeProperty(property, value) {
    if (diagramEngine.selectedNode) {
        diagramEngine.selectedNode[property] = value;
        diagramEngine.render();
        updatePropertiesPanel();
    }
}

function setupNodeEditing() {
    // تحديث النص
    document.getElementById('updateTextBtn').addEventListener('click', function() {
        const text = document.getElementById('nodeTextInput').value.trim();
        if (diagramEngine.selectedNode) {
            diagramEngine.updateNodeText(diagramEngine.selectedNode, text || 'نص جديد');
            updatePropertiesPanel();
        }
    });

    // تحديث النص أثناء الكتابة
    document.getElementById('nodeTextInput').addEventListener('input', function() {
        const text = this.value.trim();
        if (diagramEngine.selectedNode) {
            diagramEngine.updateNodeText(diagramEngine.selectedNode, text || 'نص جديد');
        }
    });

    // تحديث الصورة
    document.getElementById('updateImageBtn').addEventListener('click', function() {
        const fileInput = document.getElementById('nodeImageUpload');
        const file = fileInput.files[0];

        if (file && file.type.startsWith('image/') && diagramEngine.selectedNode) {
            const reader = new FileReader();
            reader.onload = function(e) {
                diagramEngine.updateNodeImage(diagramEngine.selectedNode, e.target.result);
                updatePropertiesPanel();
            };
            reader.readAsDataURL(file);
        }
    });

    // تحديث النص عند الضغط على Enter
    document.getElementById('nodeTextInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('updateTextBtn').click();
        }
    });
}

function setupQuickActions() {
    // مسح الكل
    document.getElementById('clearBtn').addEventListener('click', function() {
        if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
            diagramEngine.clear();
            updatePropertiesPanel();
        }
    });

    // حذف المحدد
    document.getElementById('deleteBtn').addEventListener('click', function() {
        if (diagramEngine.selectedNode || diagramEngine.selectedConnection) {
            diagramEngine.deleteSelected();
            updatePropertiesPanel();
        }
    });
}

function setupModals() {
    // إعداد النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');
    
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    // نموذج الحفظ
    document.getElementById('saveForm').addEventListener('submit', saveDiagram);
}

function showSaveModal() {
    document.getElementById('saveModal').style.display = 'block';
}

function showLoadModal() {
    loadSavedDiagrams();
    document.getElementById('loadModal').style.display = 'block';
}

function saveDiagram(e) {
    e.preventDefault();
    
    const name = document.getElementById('diagramName').value;
    const description = document.getElementById('diagramDescription').value;
    
    const diagramData = {
        name: name,
        description: description,
        data: diagramEngine.getDiagramData(),
        timestamp: new Date().toISOString()
    };
    
    // حفظ في localStorage (يمكن تطويره لاحقاً لحفظ في قاعدة البيانات)
    let savedDiagrams = JSON.parse(localStorage.getItem('savedDiagrams') || '[]');
    savedDiagrams.push(diagramData);
    localStorage.setItem('savedDiagrams', JSON.stringify(savedDiagrams));
    
    // إغلاق النافذة وإظهار رسالة نجاح
    document.getElementById('saveModal').style.display = 'none';
    alert('تم حفظ المخطط بنجاح!');
    
    // مسح النموذج
    document.getElementById('saveForm').reset();
}

function loadSavedDiagrams() {
    const savedDiagrams = JSON.parse(localStorage.getItem('savedDiagrams') || '[]');
    const container = document.getElementById('savedDiagrams');
    
    if (savedDiagrams.length === 0) {
        container.innerHTML = '<p>لا توجد مخططات محفوظة</p>';
        return;
    }
    
    container.innerHTML = '';
    savedDiagrams.forEach((diagram, index) => {
        const diagramItem = document.createElement('div');
        diagramItem.className = 'diagram-item';
        diagramItem.innerHTML = `
            <h4>${diagram.name}</h4>
            <p>${diagram.description || 'بدون وصف'}</p>
            <small>تاريخ الحفظ: ${new Date(diagram.timestamp).toLocaleDateString('ar-SA')}</small>
        `;
        
        diagramItem.addEventListener('click', function() {
            loadDiagram(diagram.data);
            document.getElementById('loadModal').style.display = 'none';
        });
        
        container.appendChild(diagramItem);
    });
}

function loadDiagram(data) {
    diagramEngine.loadDiagram(data);
    alert('تم تحميل المخطط بنجاح!');
}

function exportDiagram() {
    const imageData = diagramEngine.exportAsImage();
    
    // إنشاء رابط تحميل
    const link = document.createElement('a');
    link.download = 'diagram_' + new Date().getTime() + '.png';
    link.href = imageData;
    link.click();
}

function updatePropertiesPanel() {
    const panel = document.getElementById('nodeProperties');

    if (!diagramEngine.selectedNode && !diagramEngine.selectedConnection) {
        panel.innerHTML = '<p>لم يتم تحديد أي عقدة أو رابط</p>';
        // مسح حقول التحرير
        document.getElementById('nodeTextInput').value = '';
        return;
    }

    if (diagramEngine.selectedNode) {
        const node = diagramEngine.selectedNode;
        let html = `<h4>خصائص العقدة ${getNodeTypeName(node.shape)}</h4>`;

        html += `<p><strong>الموقع:</strong> (${Math.round(node.x)}, ${Math.round(node.y)})</p>`;
        html += `<p><strong>الحجم:</strong> ${Math.round(node.width)} × ${Math.round(node.height)}</p>`;
        html += `<p><strong>النص:</strong> ${node.text}</p>`;
        html += `<p><strong>الصورة:</strong> ${node.imageData ? 'موجودة' : 'غير موجودة'}</p>`;

        // تحديث حقل النص
        document.getElementById('nodeTextInput').value = node.text;

        panel.innerHTML = html;
    } else if (diagramEngine.selectedConnection) {
        const connection = diagramEngine.selectedConnection;
        let html = `<h4>خصائص الرابط</h4>`;

        html += `<p><strong>من:</strong> العقدة ${connection.fromNode.text}</p>`;
        html += `<p><strong>إلى:</strong> العقدة ${connection.toNode.text}</p>`;
        html += `<p><strong>سمك الخط:</strong> ${connection.strokeWidth}</p>`;

        panel.innerHTML = html;
    }
}

function getNodeTypeName(shape) {
    const types = {
        'rectangle': 'المربعة',
        'circle': 'الدائرية',
        'diamond': 'المعين'
    };
    return types[shape] || shape;
}

// تحديث لوحة الخصائص عند تغيير التحديد
setInterval(() => {
    updatePropertiesPanel();
}, 500);
