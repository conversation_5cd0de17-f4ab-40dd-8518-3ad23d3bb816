<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('خطأ في الاتصال بقاعدة البيانات');
    }
    
    $sql = "SELECT id, name, description, created_at, updated_at FROM diagrams ORDER BY updated_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    
    $diagrams = $stmt->fetchAll();
    
    // تحويل التواريخ إلى تنسيق مقروء
    foreach ($diagrams as &$diagram) {
        $diagram['created_at_formatted'] = date('Y-m-d H:i', strtotime($diagram['created_at']));
        $diagram['updated_at_formatted'] = date('Y-m-d H:i', strtotime($diagram['updated_at']));
    }
    
    echo json_encode([
        'success' => true,
        'diagrams' => $diagrams
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
