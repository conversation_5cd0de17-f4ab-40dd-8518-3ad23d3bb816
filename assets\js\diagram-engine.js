// محرك الرسم الذكي للمخططات
class DiagramEngine {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.nodes = []; // العقد الذكية
        this.connections = []; // الروابط بين العقد
        this.selectedNode = null;
        this.selectedConnection = null;
        this.currentTool = 'select';
        this.isDrawing = false;
        this.isConnecting = false;
        this.connectionStart = null;
        this.startX = 0;
        this.startY = 0;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };

        // إعدادات الرسم
        this.strokeColor = '#2c3e50';
        this.fillColor = '#ecf0f1';
        this.strokeWidth = 2;
        this.fontSize = 16;
        this.fontFamily = 'Tahoma';
        this.nodeWidth = 250;
        this.nodeHeight = 150;

        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.render();
    }
    
    setupEventListeners() {
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('dblclick', (e) => this.handleDoubleClick(e));
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Delete' && (this.selectedNode || this.selectedConnection)) {
                this.deleteSelected();
            }
            if (e.ctrlKey && e.key === 'z') {
                e.preventDefault();
                // إضافة وظيفة التراجع لاحقاً
            }
        });
    }

    // فئة العقدة الذكية
    createSmartNode(x, y, shape = 'rectangle') {
        const node = {
            id: Date.now() + Math.random(),
            x: x - this.nodeWidth / 2,
            y: y - this.nodeHeight / 2,
            width: this.nodeWidth,
            height: this.nodeHeight,
            shape: shape, // rectangle, circle, diamond
            text: 'نص جديد',
            image: null,
            imageData: null,
            strokeColor: this.strokeColor,
            fillColor: this.fillColor,
            strokeWidth: this.strokeWidth,
            fontSize: this.fontSize,
            fontFamily: this.fontFamily,
            textColor: '#2c3e50',
            connectionPoints: this.getConnectionPoints(x - this.nodeWidth / 2, y - this.nodeHeight / 2, this.nodeWidth, this.nodeHeight)
        };

        this.nodes.push(node);
        this.selectedNode = node;
        return node;
    }

    // نقاط الاتصال للعقدة
    getConnectionPoints(x, y, width, height) {
        return {
            top: { x: x + width / 2, y: y },
            right: { x: x + width, y: y + height / 2 },
            bottom: { x: x + width / 2, y: y + height },
            left: { x: x, y: y + height / 2 }
        };
    }
    
    getMousePos(e) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
    }
    
    handleMouseDown(e) {
        const pos = this.getMousePos(e);
        this.startX = pos.x;
        this.startY = pos.y;

        if (this.currentTool === 'select') {
            const node = this.getNodeAt(pos.x, pos.y);
            if (node) {
                this.selectedNode = node;
                this.selectedConnection = null;
                this.isDragging = true;
                this.dragOffset = {
                    x: pos.x - node.x,
                    y: pos.y - node.y
                };
            } else {
                // التحقق من النقر على رابط
                const connection = this.getConnectionAt(pos.x, pos.y);
                if (connection) {
                    this.selectedConnection = connection;
                    this.selectedNode = null;
                } else {
                    this.selectedNode = null;
                    this.selectedConnection = null;
                }
            }
        } else if (this.currentTool === 'connect') {
            const node = this.getNodeAt(pos.x, pos.y);
            if (node) {
                if (!this.connectionStart) {
                    this.connectionStart = node;
                    this.isConnecting = true;
                } else if (this.connectionStart !== node) {
                    this.createConnection(this.connectionStart, node);
                    this.connectionStart = null;
                    this.isConnecting = false;
                }
            }
        } else {
            // إنشاء عقدة جديدة
            this.createSmartNode(pos.x, pos.y, this.currentTool);
        }

        this.render();
    }
    
    handleMouseMove(e) {
        const pos = this.getMousePos(e);

        if (this.isDragging && this.selectedNode) {
            this.selectedNode.x = pos.x - this.dragOffset.x;
            this.selectedNode.y = pos.y - this.dragOffset.y;
            // تحديث نقاط الاتصال
            this.selectedNode.connectionPoints = this.getConnectionPoints(
                this.selectedNode.x, this.selectedNode.y,
                this.selectedNode.width, this.selectedNode.height
            );
            this.render();
        } else if (this.isConnecting) {
            this.render();
            this.drawConnectionPreview(this.connectionStart, pos);
        }
    }

    // إنشاء رابط بين عقدتين
    createConnection(fromNode, toNode) {
        const connection = {
            id: Date.now() + Math.random(),
            from: fromNode.id,
            to: toNode.id,
            fromNode: fromNode,
            toNode: toNode,
            strokeColor: this.strokeColor,
            strokeWidth: this.strokeWidth,
            arrowSize: 15
        };

        this.connections.push(connection);
        return connection;
    }

    // رسم معاينة الرابط
    drawConnectionPreview(fromNode, toPos) {
        if (!fromNode) return;

        const fromPoint = fromNode.connectionPoints.right;

        this.ctx.save();
        this.ctx.strokeStyle = '#3498db';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);

        this.ctx.beginPath();
        this.ctx.moveTo(fromPoint.x, fromPoint.y);
        this.ctx.lineTo(toPos.x, toPos.y);
        this.ctx.stroke();

        this.ctx.restore();
    }
    
    handleMouseUp(e) {
        const pos = this.getMousePos(e);
        
        if (this.isDrawing) {
            this.createElement(this.startX, this.startY, pos.x, pos.y);
            this.isDrawing = false;
        }
        
        this.isDragging = false;
        this.render();
    }
    
    handleDoubleClick(e) {
        if (this.currentTool === 'text') {
            const pos = this.getMousePos(e);
            const text = prompt('أدخل النص:');
            if (text) {
                this.addTextElement(pos.x, pos.y, text);
            }
        }
    }
    
    createElement(startX, startY, endX, endY) {
        const element = {
            id: Date.now(),
            type: this.currentTool,
            x: Math.min(startX, endX),
            y: Math.min(startY, endY),
            width: Math.abs(endX - startX),
            height: Math.abs(endY - startY),
            strokeColor: this.strokeColor,
            fillColor: this.fillColor,
            strokeWidth: this.strokeWidth
        };
        
        if (this.currentTool === 'arrow') {
            element.startX = startX;
            element.startY = startY;
            element.endX = endX;
            element.endY = endY;
        }
        
        // هذه الوظيفة لم تعد مستخدمة في النظام الجديد
    }
    
    addTextElement(x, y, text) {
        const element = {
            id: Date.now(),
            type: 'text',
            x: x,
            y: y,
            text: text,
            fontSize: this.fontSize,
            fontFamily: this.fontFamily,
            color: this.strokeColor
        };
        
        // هذه الوظيفة لم تعد مستخدمة في النظام الجديد
    }
    
    addImageElement(x, y, imageData) {
        const element = {
            id: Date.now(),
            type: 'image',
            x: x,
            y: y,
            imageData: imageData,
            width: 100,
            height: 100
        };
        
        // هذه الوظيفة لم تعد مستخدمة في النظام الجديد
    }
    
    // هذه الوظيفة لم تعد مستخدمة - استخدم getNodeAt بدلاً منها
    
    isPointInElement(x, y, element) {
        switch (element.type) {
            case 'rectangle':
            case 'image':
                return x >= element.x && x <= element.x + element.width &&
                       y >= element.y && y <= element.y + element.height;
            case 'circle':
                const centerX = element.x + element.width / 2;
                const centerY = element.y + element.height / 2;
                const radius = Math.min(element.width, element.height) / 2;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                return distance <= radius;
            case 'text':
                this.ctx.font = `${element.fontSize}px ${element.fontFamily}`;
                const textWidth = this.ctx.measureText(element.text).width;
                return x >= element.x && x <= element.x + textWidth &&
                       y >= element.y - element.fontSize && y <= element.y;
            default:
                return false;
        }
    }
    
    drawPreview(startX, startY, endX, endY) {
        this.ctx.save();
        this.ctx.strokeStyle = this.strokeColor;
        this.ctx.fillStyle = this.fillColor;
        this.ctx.lineWidth = this.strokeWidth;
        this.ctx.setLineDash([5, 5]);
        
        switch (this.currentTool) {
            case 'rectangle':
                this.drawRectangle(startX, startY, endX - startX, endY - startY);
                break;
            case 'circle':
                this.drawCircle(startX, startY, endX - startX, endY - startY);
                break;
            case 'arrow':
                this.drawArrow(startX, startY, endX, endY);
                break;
        }
        
        this.ctx.restore();
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم جميع الروابط أولاً
        this.connections.forEach(connection => {
            this.drawConnection(connection);
        });

        // رسم جميع العقد
        this.nodes.forEach(node => {
            this.drawSmartNode(node);
        });

        // رسم حدود العقدة المحددة
        if (this.selectedNode) {
            this.drawNodeSelectionBorder(this.selectedNode);
        }

        // رسم تمييز الرابط المحدد
        if (this.selectedConnection) {
            this.drawConnectionHighlight(this.selectedConnection);
        }
    }

    // رسم العقدة الذكية
    drawSmartNode(node) {
        this.ctx.save();

        // رسم الشكل الأساسي
        this.ctx.strokeStyle = node.strokeColor;
        this.ctx.fillStyle = node.fillColor;
        this.ctx.lineWidth = node.strokeWidth;

        switch (node.shape) {
            case 'rectangle':
                this.drawRectangleNode(node);
                break;
            case 'circle':
                this.drawCircleNode(node);
                break;
            case 'diamond':
                this.drawDiamondNode(node);
                break;
        }

        // رسم الصورة إذا كانت موجودة
        if (node.imageData) {
            this.drawNodeImage(node);
        }

        // رسم النص
        this.drawNodeText(node);

        // رسم نقاط الاتصال إذا كانت العقدة محددة
        if (this.selectedNode === node) {
            this.drawConnectionPoints(node);
        }

        this.ctx.restore();
    }
    
    drawElement(element) {
        this.ctx.save();
        
        switch (element.type) {
            case 'rectangle':
                this.ctx.strokeStyle = element.strokeColor;
                this.ctx.fillStyle = element.fillColor;
                this.ctx.lineWidth = element.strokeWidth;
                this.drawRectangle(element.x, element.y, element.width, element.height);
                break;
                
            case 'circle':
                this.ctx.strokeStyle = element.strokeColor;
                this.ctx.fillStyle = element.fillColor;
                this.ctx.lineWidth = element.strokeWidth;
                this.drawCircle(element.x, element.y, element.width, element.height);
                break;
                
            case 'arrow':
                this.ctx.strokeStyle = element.strokeColor;
                this.ctx.lineWidth = element.strokeWidth;
                this.drawArrow(element.startX, element.startY, element.endX, element.endY);
                break;
                
            case 'text':
                this.ctx.fillStyle = element.color;
                this.ctx.font = `${element.fontSize}px ${element.fontFamily}`;
                this.ctx.textAlign = 'right';
                this.ctx.fillText(element.text, element.x, element.y);
                break;
                
            case 'image':
                if (element.imageData) {
                    const img = new Image();
                    img.onload = () => {
                        this.ctx.drawImage(img, element.x, element.y, element.width, element.height);
                    };
                    img.src = element.imageData;
                }
                break;
        }
        
        this.ctx.restore();
    }
    
    drawRectangle(x, y, width, height) {
        this.ctx.fillRect(x, y, width, height);
        this.ctx.strokeRect(x, y, width, height);
    }
    
    drawCircle(x, y, width, height) {
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        const radius = Math.min(width, height) / 2;
        
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.fill();
        this.ctx.stroke();
    }
    
    drawArrow(startX, startY, endX, endY) {
        const headLength = 15;
        const angle = Math.atan2(endY - startY, endX - startX);
        
        // رسم الخط الرئيسي
        this.ctx.beginPath();
        this.ctx.moveTo(startX, startY);
        this.ctx.lineTo(endX, endY);
        this.ctx.stroke();
        
        // رسم رأس السهم
        this.ctx.beginPath();
        this.ctx.moveTo(endX, endY);
        this.ctx.lineTo(
            endX - headLength * Math.cos(angle - Math.PI / 6),
            endY - headLength * Math.sin(angle - Math.PI / 6)
        );
        this.ctx.moveTo(endX, endY);
        this.ctx.lineTo(
            endX - headLength * Math.cos(angle + Math.PI / 6),
            endY - headLength * Math.sin(angle + Math.PI / 6)
        );
        this.ctx.stroke();
    }
    
    drawSelectionBorder(element) {
        this.ctx.save();
        this.ctx.strokeStyle = '#3498db';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        
        if (element.type === 'text') {
            this.ctx.font = `${element.fontSize}px ${element.fontFamily}`;
            const textWidth = this.ctx.measureText(element.text).width;
            this.ctx.strokeRect(element.x - 5, element.y - element.fontSize - 5, 
                              textWidth + 10, element.fontSize + 10);
        } else {
            this.ctx.strokeRect(element.x - 5, element.y - 5, 
                              element.width + 10, element.height + 10);
        }
        
        this.ctx.restore();
    }
    
    // هذه الوظيفة تم استبدالها بوظيفة deleteSelected الجديدة
    
    // هذه الوظيفة تم استبدالها بوظيفة clear الجديدة
    
    exportAsImage() {
        return this.canvas.toDataURL('image/png');
    }
    
    // هذه الوظيفة تم استبدالها بوظيفة loadDiagram الجديدة
    
    // رسم مربع/مستطيل للعقدة
    drawRectangleNode(node) {
        this.ctx.fillRect(node.x, node.y, node.width, node.height);
        this.ctx.strokeRect(node.x, node.y, node.width, node.height);
    }

    // رسم دائرة للعقدة
    drawCircleNode(node) {
        const centerX = node.x + node.width / 2;
        const centerY = node.y + node.height / 2;
        const radius = Math.min(node.width, node.height) / 2;

        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        this.ctx.fill();
        this.ctx.stroke();
    }

    // رسم معين للعقدة
    drawDiamondNode(node) {
        const centerX = node.x + node.width / 2;
        const centerY = node.y + node.height / 2;

        this.ctx.beginPath();
        this.ctx.moveTo(centerX, node.y); // أعلى
        this.ctx.lineTo(node.x + node.width, centerY); // يمين
        this.ctx.lineTo(centerX, node.y + node.height); // أسفل
        this.ctx.lineTo(node.x, centerY); // يسار
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    // رسم النص في العقدة
    drawNodeText(node) {
        if (!node.text || node.text.trim() === '') return;

        this.ctx.save();
        this.ctx.fillStyle = node.textColor;
        this.ctx.font = `${node.fontSize}px ${node.fontFamily}`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        const centerX = node.x + node.width / 2;
        const centerY = node.y + node.height / 2;

        // تقسيم النص إلى أسطر
        const words = node.text.split(' ');
        const maxWidth = node.width - 40; // زيادة المساحة الجانبية
        let lines = [];
        let currentLine = '';

        words.forEach(word => {
            const testLine = currentLine + (currentLine ? ' ' : '') + word;
            const metrics = this.ctx.measureText(testLine);

            if (metrics.width > maxWidth && currentLine) {
                lines.push(currentLine);
                currentLine = word;
            } else {
                currentLine = testLine;
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        // رسم خلفية شفافة للنص لضمان الوضوح
        const lineHeight = node.fontSize + 6;
        const totalHeight = lines.length * lineHeight;
        const textAreaHeight = totalHeight + 10;
        const textAreaY = centerY - textAreaHeight / 2;

        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.fillRect(node.x + 10, textAreaY, node.width - 20, textAreaHeight);

        // رسم الأسطر
        this.ctx.fillStyle = node.textColor;
        const startY = centerY - totalHeight / 2 + lineHeight / 2;

        lines.forEach((line, index) => {
            this.ctx.fillText(line, centerX, startY + index * lineHeight);
        });

        this.ctx.restore();
    }

    // رسم الصورة في العقدة
    drawNodeImage(node) {
        if (!node.imageData) return;

        // إذا كانت الصورة محملة مسبقاً، ارسمها مباشرة
        if (node.loadedImage) {
            this.drawLoadedImage(node, node.loadedImage);
            return;
        }

        // تحميل الصورة لأول مرة
        const img = new Image();
        img.onload = () => {
            node.loadedImage = img; // حفظ الصورة المحملة
            this.drawLoadedImage(node, img);
            this.render(); // إعادة رسم الكانفاس بعد تحميل الصورة
        };
        img.onerror = () => {
            console.error('فشل في تحميل الصورة');
        };
        img.src = node.imageData;
    }

    // رسم الصورة المحملة
    drawLoadedImage(node, img) {
        const imageSize = Math.min(node.width - 20, node.height - 20, 80);
        const imageX = node.x + (node.width - imageSize) / 2;
        const imageY = node.y + 10;

        this.ctx.save();

        // رسم خلفية للصورة
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.fillRect(imageX - 2, imageY - 2, imageSize + 4, imageSize + 4);
        this.ctx.strokeStyle = '#bdc3c7';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(imageX - 2, imageY - 2, imageSize + 4, imageSize + 4);

        // قص الصورة في شكل دائري
        this.ctx.beginPath();
        this.ctx.arc(imageX + imageSize/2, imageY + imageSize/2, imageSize/2, 0, 2 * Math.PI);
        this.ctx.clip();

        // رسم الصورة
        this.ctx.drawImage(img, imageX, imageY, imageSize, imageSize);

        this.ctx.restore();
    }

    // رسم نقاط الاتصال
    drawConnectionPoints(node) {
        this.ctx.save();
        this.ctx.fillStyle = '#3498db';
        this.ctx.strokeStyle = '#2980b9';
        this.ctx.lineWidth = 2;

        Object.values(node.connectionPoints).forEach(point => {
            this.ctx.beginPath();
            this.ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI);
            this.ctx.fill();
            this.ctx.stroke();
        });

        this.ctx.restore();
    }

    // رسم الرابط
    drawConnection(connection) {
        const fromPoint = this.getOptimalConnectionPoint(connection.fromNode, connection.toNode);
        const toPoint = this.getOptimalConnectionPoint(connection.toNode, connection.fromNode);

        this.ctx.save();
        this.ctx.strokeStyle = connection.strokeColor;
        this.ctx.lineWidth = connection.strokeWidth;

        // رسم الخط
        this.ctx.beginPath();
        this.ctx.moveTo(fromPoint.x, fromPoint.y);
        this.ctx.lineTo(toPoint.x, toPoint.y);
        this.ctx.stroke();

        // رسم رأس السهم
        this.drawArrowHead(fromPoint, toPoint, connection.arrowSize);

        this.ctx.restore();
    }

    // الحصول على أفضل نقطة اتصال
    getOptimalConnectionPoint(node, targetNode) {
        const nodeCenterX = node.x + node.width / 2;
        const nodeCenterY = node.y + node.height / 2;
        const targetCenterX = targetNode.x + targetNode.width / 2;
        const targetCenterY = targetNode.y + targetNode.height / 2;

        if (targetCenterX > nodeCenterX) {
            return node.connectionPoints.right;
        } else if (targetCenterX < nodeCenterX) {
            return node.connectionPoints.left;
        } else if (targetCenterY > nodeCenterY) {
            return node.connectionPoints.bottom;
        } else {
            return node.connectionPoints.top;
        }
    }

    // رسم رأس السهم
    drawArrowHead(fromPoint, toPoint, size) {
        const angle = Math.atan2(toPoint.y - fromPoint.y, toPoint.x - fromPoint.x);

        this.ctx.beginPath();
        this.ctx.moveTo(toPoint.x, toPoint.y);
        this.ctx.lineTo(
            toPoint.x - size * Math.cos(angle - Math.PI / 6),
            toPoint.y - size * Math.sin(angle - Math.PI / 6)
        );
        this.ctx.moveTo(toPoint.x, toPoint.y);
        this.ctx.lineTo(
            toPoint.x - size * Math.cos(angle + Math.PI / 6),
            toPoint.y - size * Math.sin(angle + Math.PI / 6)
        );
        this.ctx.stroke();
    }

    // البحث عن عقدة في موقع معين
    getNodeAt(x, y) {
        for (let i = this.nodes.length - 1; i >= 0; i--) {
            const node = this.nodes[i];
            if (this.isPointInNode(x, y, node)) {
                return node;
            }
        }
        return null;
    }

    // التحقق من وجود نقطة داخل عقدة
    isPointInNode(x, y, node) {
        switch (node.shape) {
            case 'rectangle':
            case 'diamond':
                return x >= node.x && x <= node.x + node.width &&
                       y >= node.y && y <= node.y + node.height;
            case 'circle':
                const centerX = node.x + node.width / 2;
                const centerY = node.y + node.height / 2;
                const radius = Math.min(node.width, node.height) / 2;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                return distance <= radius;
            default:
                return false;
        }
    }

    // البحث عن رابط في موقع معين
    getConnectionAt(x, y) {
        for (let connection of this.connections) {
            if (this.isPointOnConnection(x, y, connection)) {
                return connection;
            }
        }
        return null;
    }

    // التحقق من وجود نقطة على رابط
    isPointOnConnection(x, y, connection) {
        const fromPoint = this.getOptimalConnectionPoint(connection.fromNode, connection.toNode);
        const toPoint = this.getOptimalConnectionPoint(connection.toNode, connection.fromNode);

        // حساب المسافة من النقطة إلى الخط
        const A = toPoint.y - fromPoint.y;
        const B = fromPoint.x - toPoint.x;
        const C = toPoint.x * fromPoint.y - fromPoint.x * toPoint.y;

        const distance = Math.abs(A * x + B * y + C) / Math.sqrt(A * A + B * B);

        return distance <= 5; // مسافة التسامح
    }

    // رسم حدود العقدة المحددة
    drawNodeSelectionBorder(node) {
        this.ctx.save();
        this.ctx.strokeStyle = '#3498db';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([8, 4]);

        this.ctx.strokeRect(node.x - 5, node.y - 5, node.width + 10, node.height + 10);

        this.ctx.restore();
    }

    // تمييز الرابط المحدد
    drawConnectionHighlight(connection) {
        const fromPoint = this.getOptimalConnectionPoint(connection.fromNode, connection.toNode);
        const toPoint = this.getOptimalConnectionPoint(connection.toNode, connection.fromNode);

        this.ctx.save();
        this.ctx.strokeStyle = '#e74c3c';
        this.ctx.lineWidth = connection.strokeWidth + 2;
        this.ctx.setLineDash([8, 4]);

        this.ctx.beginPath();
        this.ctx.moveTo(fromPoint.x, fromPoint.y);
        this.ctx.lineTo(toPoint.x, toPoint.y);
        this.ctx.stroke();

        this.ctx.restore();
    }

    // حذف العنصر المحدد
    deleteSelected() {
        if (this.selectedNode) {
            // حذف العقدة والروابط المرتبطة بها
            this.connections = this.connections.filter(conn =>
                conn.from !== this.selectedNode.id && conn.to !== this.selectedNode.id
            );

            const index = this.nodes.findIndex(node => node.id === this.selectedNode.id);
            if (index > -1) {
                this.nodes.splice(index, 1);
                this.selectedNode = null;
                this.render();
            }
        } else if (this.selectedConnection) {
            // حذف الرابط
            const index = this.connections.findIndex(conn => conn.id === this.selectedConnection.id);
            if (index > -1) {
                this.connections.splice(index, 1);
                this.selectedConnection = null;
                this.render();
            }
        }
    }

    // تحديث نص العقدة
    updateNodeText(node, text) {
        node.text = text;
        this.render();
    }

    // تحديث صورة العقدة
    updateNodeImage(node, imageData) {
        node.imageData = imageData;
        this.render();
    }

    // مسح جميع العناصر
    clear() {
        this.nodes = [];
        this.connections = [];
        this.selectedNode = null;
        this.selectedConnection = null;
        this.render();
    }

    // تصدير كصورة
    exportAsImage() {
        return this.canvas.toDataURL('image/png');
    }

    // تحميل مخطط
    loadDiagram(data) {
        this.nodes = data.nodes || [];
        this.connections = data.connections || [];
        this.selectedNode = null;
        this.selectedConnection = null;
        this.render();
    }

    getDiagramData() {
        return {
            nodes: this.nodes,
            connections: this.connections,
            timestamp: new Date().toISOString()
        };
    }

    // البحث عن عقدة في موقع معين
    getNodeAt(x, y) {
        for (let i = this.nodes.length - 1; i >= 0; i--) {
            const node = this.nodes[i];
            if (this.isPointInNode(x, y, node)) {
                return node;
            }
        }
        return null;
    }

    // التحقق من وجود نقطة داخل عقدة
    isPointInNode(x, y, node) {
        switch (node.shape) {
            case 'rectangle':
                return x >= node.x && x <= node.x + node.width &&
                       y >= node.y && y <= node.y + node.height;
            case 'circle':
                const centerX = node.x + node.width / 2;
                const centerY = node.y + node.height / 2;
                const radius = Math.min(node.width, node.height) / 2;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                return distance <= radius;
            case 'diamond':
                // تحقق مبسط للمعين
                const cx = node.x + node.width / 2;
                const cy = node.y + node.height / 2;
                const dx = Math.abs(x - cx) / (node.width / 2);
                const dy = Math.abs(y - cy) / (node.height / 2);
                return (dx + dy) <= 1;
            default:
                return false;
        }
    }

    // البحث عن رابط في موقع معين
    getConnectionAt(x, y) {
        for (let connection of this.connections) {
            if (this.isPointOnConnection(x, y, connection)) {
                return connection;
            }
        }
        return null;
    }

    // التحقق من وجود نقطة على رابط
    isPointOnConnection(x, y, connection) {
        const fromCenter = {
            x: connection.fromNode.x + connection.fromNode.width / 2,
            y: connection.fromNode.y + connection.fromNode.height / 2
        };
        const toCenter = {
            x: connection.toNode.x + connection.toNode.width / 2,
            y: connection.toNode.y + connection.toNode.height / 2
        };

        // حساب المسافة من النقطة إلى الخط
        const A = toCenter.y - fromCenter.y;
        const B = fromCenter.x - toCenter.x;
        const C = toCenter.x * fromCenter.y - fromCenter.x * toCenter.y;
        const distance = Math.abs(A * x + B * y + C) / Math.sqrt(A * A + B * B);

        return distance <= 5; // مسافة تسامح 5 بكسل
    }

    // حذف العنصر المحدد
    deleteSelected() {
        if (this.selectedNode) {
            // حذف العقدة
            const nodeIndex = this.nodes.findIndex(node => node.id === this.selectedNode.id);
            if (nodeIndex > -1) {
                this.nodes.splice(nodeIndex, 1);
            }

            // حذف جميع الروابط المتصلة بهذه العقدة
            this.connections = this.connections.filter(connection =>
                connection.from !== this.selectedNode.id &&
                connection.to !== this.selectedNode.id
            );

            this.selectedNode = null;
        } else if (this.selectedConnection) {
            // حذف الرابط
            const connectionIndex = this.connections.findIndex(connection =>
                connection.id === this.selectedConnection.id
            );
            if (connectionIndex > -1) {
                this.connections.splice(connectionIndex, 1);
            }
            this.selectedConnection = null;
        }

        this.render();
    }

    // مسح جميع العناصر
    clear() {
        this.nodes = [];
        this.connections = [];
        this.selectedNode = null;
        this.selectedConnection = null;
        this.render();
    }

    // تحديث نص العقدة
    updateNodeText(node, text) {
        if (node) {
            node.text = text;
            this.render();
        }
    }

    // تحديث صورة العقدة
    updateNodeImage(node, imageData) {
        if (node) {
            console.log('🖼️ تحديث صورة العقدة...');
            node.imageData = imageData;
            node.loadedImage = null; // إعادة تعيين الصورة المحملة
            this.render();
            console.log('✅ تم تحديث صورة العقدة بنجاح');
        } else {
            console.warn('⚠️ لم يتم تحديد عقدة لتحديث الصورة');
        }
    }
}
