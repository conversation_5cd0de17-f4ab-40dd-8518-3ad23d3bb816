<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

require_once '../config/database.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المخطط مطلوب']);
    exit;
}

try {
    $diagram_id = (int)$_GET['id'];
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('خطأ في الاتصال بقاعدة البيانات');
    }
    
    $sql = "SELECT * FROM diagrams WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$diagram_id]);
    
    $diagram = $stmt->fetch();
    
    if (!$diagram) {
        throw new Exception('المخطط غير موجود');
    }
    
    // فك تشفير بيانات المخطط
    $diagram['diagram_data'] = json_decode($diagram['diagram_data'], true);
    $diagram['created_at_formatted'] = date('Y-m-d H:i', strtotime($diagram['created_at']));
    $diagram['updated_at_formatted'] = date('Y-m-d H:i', strtotime($diagram['updated_at']));
    
    echo json_encode([
        'success' => true,
        'diagram' => $diagram
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
