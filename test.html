<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق</title>
    <style>
        body { font-family: Tahoma; padding: 20px; }
        canvas { border: 1px solid #ccc; }
        .error { color: red; font-weight: bold; }
        .success { color: green; font-weight: bold; }
    </style>
</head>
<body>
    <h1>اختبار تطبيق المخططات</h1>
    <div id="status">جاري التحميل...</div>
    <canvas id="testCanvas" width="400" height="300"></canvas>
    
    <script>
        try {
            console.log('بدء اختبار JavaScript...');
            
            // اختبار Canvas
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            if (ctx) {
                // رسم مربع اختبار
                ctx.fillStyle = '#3498db';
                ctx.fillRect(50, 50, 100, 100);
                
                // رسم نص عربي
                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Tahoma';
                ctx.textAlign = 'center';
                ctx.fillText('اختبار النص العربي', 200, 200);
                
                document.getElementById('status').innerHTML = '<span class="success">✅ Canvas يعمل بشكل صحيح!</span>';
            } else {
                throw new Error('فشل في تهيئة Canvas');
            }
            
        } catch (error) {
            console.error('خطأ:', error);
            document.getElementById('status').innerHTML = '<span class="error">❌ خطأ: ' + error.message + '</span>';
        }
    </script>
</body>
</html>
