<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التخطيط</title>
    <style>
        body { 
            font-family: Tahoma; 
            margin: 0; 
            padding: 0;
            background: #f5f5f5;
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .toolbar {
            background: white;
            padding: 1rem;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 1.5rem;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            flex-wrap: wrap;
        }
        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #ecf0f1;
            padding: 1rem;
            overflow: auto;
            position: relative;
            height: calc(100vh - 350px);
        }
        .canvas {
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 1200px;
            height: 600px;
            max-width: 100%;
            max-height: 100%;
            display: block;
        }
        .properties-panel {
            background: white;
            padding: 1rem;
            border-top: 1px solid #ddd;
            min-height: 120px;
            max-height: 250px;
            overflow-y: auto;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            background: #3498db;
            color: white;
            margin: 0 0.25rem;
        }
        .tool-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 150px;
        }
        .tool-group h3 {
            font-size: 0.9rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.2rem;
        }
        .tool-btn {
            padding: 0.5rem;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.8rem;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                gap: 1rem;
                padding: 0.5rem;
            }
            .header {
                flex-direction: column;
                gap: 1rem;
                padding: 0.5rem;
            }
            .canvas-container {
                height: calc(100vh - 400px);
                padding: 0.5rem;
            }
            .canvas {
                width: 100% !important;
                height: 100% !important;
            }
            .properties-panel {
                max-height: 120px;
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 تطبيق المخططات التقنية</h1>
            <div>
                <button class="btn">💾 حفظ كصورة</button>
                <button class="btn">📁 تحميل مخطط</button>
                <button class="btn">💾 حفظ المخطط</button>
            </div>
        </div>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="tool-group">
                <h3>الأدوات الذكية</h3>
                <button class="tool-btn">🟦 عقدة مربعة</button>
                <button class="tool-btn">🔴 عقدة دائرية</button>
                <button class="tool-btn">🟨 عقدة معين</button>
                <button class="tool-btn">🔗 ربط العقد</button>
            </div>
            
            <div class="tool-group">
                <h3>إجراءات سريعة</h3>
                <button class="tool-btn">🗑️ مسح الكل</button>
                <button class="tool-btn">❌ حذف المحدد</button>
            </div>
            
            <div class="tool-group">
                <h3>تحرير العقدة المحددة</h3>
                <input type="text" placeholder="أدخل النص هنا..." style="margin-bottom: 0.5rem;">
                <button class="tool-btn">📝 تحديث النص</button>
                <button class="tool-btn">🖼️ تحديث الصورة</button>
            </div>
            
            <div class="tool-group">
                <h3>الخصائص</h3>
                <label>لون الحدود:</label>
                <input type="color" value="#2c3e50">
                <label>سمك الخط:</label>
                <input type="range" min="1" max="10" value="2">
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-container">
            <div class="canvas">
                <div class="status">
                    ✅ مساحة العمل جاهزة - الحجم متناسب مع الشاشة
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <h3>خصائص العقدة المحددة</h3>
            <div id="nodeProperties">
                <p>لم يتم تحديد أي عقدة أو رابط</p>
            </div>
            
            <div class="instructions">
                <h4>كيفية الاستخدام:</h4>
                <ul>
                    <li><strong>إنشاء عقدة:</strong> اختر نوع العقدة وانقر في أي مكان على مساحة العمل</li>
                    <li><strong>تحرير النص:</strong> حدد العقدة واكتب النص في الحقل أعلاه</li>
                    <li><strong>ربط العقد:</strong> اختر أداة الربط وانقر على عقدتين</li>
                    <li><strong>تحريك العقد:</strong> اسحب العقدة لتحريكها</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // اختبار الاستجابة
        function checkLayout() {
            const container = document.querySelector('.canvas-container');
            const canvas = document.querySelector('.canvas');
            const status = document.querySelector('.status');
            
            const containerRect = container.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            status.innerHTML = `
                ✅ مساحة العمل: ${Math.round(containerRect.width)} × ${Math.round(containerRect.height)}<br>
                📐 الكانفاس: ${Math.round(canvasRect.width)} × ${Math.round(canvasRect.height)}<br>
                📱 نوع الشاشة: ${window.innerWidth < 768 ? 'موبايل' : 'سطح المكتب'}
            `;
        }
        
        // تحديث عند تحميل الصفحة وتغيير حجم النافذة
        window.addEventListener('load', checkLayout);
        window.addEventListener('resize', checkLayout);
        
        // تحديث كل ثانية
        setInterval(checkLayout, 1000);
    </script>
</body>
</html>
